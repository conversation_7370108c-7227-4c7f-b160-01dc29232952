/* eslint-disable sonarjs/no-nested-switch -- I think this is fine */
import { AsyncActionType, AsyncEventType } from '@drata/enums';
import { t } from '@globals/i18n/macro';

const downloadActionsType = [
    AsyncActionType.RISK_ASSESSMENT_REPORT_GENERATED,
    AsyncActionType.RISK_CSV_GENERATED,
    AsyncActionType.RISK_DASHBOARD_REPORT_GENERATED,
    AsyncActionType.RISKS_ASSESSMENT_LEGACY_REPORT_GENERATED,
    AsyncActionType.TRUST_CENTER_REPORT_GENERATED,
    AsyncActionType.VULNERABILITY_MONITORING_CSV_GENERATED,
    AsyncActionType.ASSETS_CSV_GENERATED,
    AsyncActionType.PERSONNEL_COMPLIANCE_OVERVIEW_REPORT_GENERATED,
    AsyncActionType.PERSONNEL_POLICY_ACKNOWLEDGMENT_REPORT_GENERATED,
    AsyncActionType.EVENT_DETAILS_FILE_GENERATED,
    AsyncActionType.ALL_POLICIES_FILE_GENERATED,
    AsyncActionType.GRC_CONTROL_DOWNLOAD_ALL_EVIDENCE,
    AsyncActionType.GRC_CONTROL_DOWNLOAD_POLICY_EVIDENCE,
    AsyncActionType.GRC_CONTROL_DOWNLOAD_ONLY_EVIDENCE,
    AsyncActionType.EVIDENCE_LIBRARY_ZIP_GENERATED,
    AsyncActionType.VENDOR_DOWNLOAD_REPORT_GENERATED,
    AsyncActionType.VENDOR_PROSPECTIVE_DOWNLOAD_REPORT_GENERATED,
] as const satisfies AsyncActionType[];

const getDownloadTypeDescriptor = (
    type: AsyncActionType,
    eventType: AsyncEventType,
): {
    title: string;
    link?: { label: string };
    message: string | null;
    severity: 'success' | 'critical' | 'primary';
} | null => {
    switch (type) {
        case AsyncActionType.RISK_ASSESSMENT_REPORT_GENERATED:
        case AsyncActionType.RISK_CSV_GENERATED:
        case AsyncActionType.RISK_DASHBOARD_REPORT_GENERATED:
        case AsyncActionType.RISKS_ASSESSMENT_LEGACY_REPORT_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Risk report queued',
                        message:
                            "We're preparing your report and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your risk report is ready',
                        link: { label: 'Download' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download risk report",
                        link: { label: 'Try again' },
                        message: '',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.TRUST_CENTER_REPORT_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Trust Center report queued',
                        message:
                            "We're preparing your report and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your Trust Center report is ready',
                        link: { label: 'Download report' },
                        message:
                            'You can download your report here or find it in your email.',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download Trust Center report",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.VULNERABILITY_MONITORING_CSV_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Vulnerabilities report queued',
                        message:
                            "We're preparing your report and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your vulnerabilities report is ready',
                        link: { label: 'Download' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download vulnerabilities report",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.ASSETS_CSV_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: t`Assets CSV queued`,
                        message: t`We're preparing your CSV and will notify you when it's ready.`,
                        severity: 'primary',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: t`Your assets CSV is ready`,
                        link: { label: t`Download` },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: t`Couldn't download assets CSV`,
                        link: { label: t`Try again` },
                        message: t`Please try again later.`,
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.PERSONNEL_COMPLIANCE_OVERVIEW_REPORT_GENERATED:
        case AsyncActionType.PERSONNEL_POLICY_ACKNOWLEDGMENT_REPORT_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Personnel report download queued',
                        message:
                            "We're preparing your csv and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your personnel csv is ready',
                        link: { label: 'Download' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download personnel report",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.EVENT_DETAILS_FILE_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Event details file download queued',
                        message:
                            "We're preparing your file and will notify you when it's ready.",
                        severity: 'primary',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your event details file is ready',
                        link: { label: 'Download' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download event details file",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.EVENT_DETAILS_FILE_PREVIEW: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Your file is being prepared.',
                        message:
                            "We're preparing your file and will notify you when it's ready.",
                        severity: 'primary',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your file is ready to view.',
                        link: { label: 'Open File' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't prepare file for viewing",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.ALL_POLICIES_FILE_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'All policies download queued',
                        message:
                            "We're preparing your zip file and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your policies zip file is ready',
                        link: { label: 'Download' },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download policies zip file",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.GRC_CONTROL_DOWNLOAD_ALL_EVIDENCE: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Downloading evidence',
                        message:
                            "We're preparing your evidence and will notify you when it's ready.",
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your evidence is ready',
                        link: { label: 'Download' },
                        message:
                            'Click the link below to get your evidence file',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download evidence",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.GRC_CONTROL_DOWNLOAD_ONLY_EVIDENCE: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Downloading control evidence',
                        message:
                            "We're preparing your evidence and will notify you when it's ready.",
                        severity: 'primary',
                    };
                }

                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your control evidence is ready',
                        link: { label: 'Download' },
                        message:
                            'Click the link below to get your control evidence file',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download evidence",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.GRC_CONTROL_DOWNLOAD_POLICY_EVIDENCE: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: 'Downloading policy',
                        message:
                            "We're preparing your policy and will notify you when it's ready.",
                        severity: 'primary',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: 'Your policy is ready',
                        link: { label: 'Download' },
                        message: 'Click the link below to get your policy file',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: "Couldn't download policy",
                        link: { label: 'Try again' },
                        message: 'Please try again later.',
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.EVIDENCE_LIBRARY_ZIP_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: t`The evidence archive is being processed`,
                        message: t`We're preparing your evidence archive and will notify you when it's ready.`,
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: t`Your evidence archive is ready`,
                        link: { label: t`Download` },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: t`Couldn't download evidence archive`,
                        link: { label: t`Try again` },
                        message: t`Please try again later.`,
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.VENDOR_DOWNLOAD_REPORT_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: t`The vendor archive is being processed`,
                        message: t`We're preparing your vendor archive and will notify you when it's ready.`,
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: t`Your vendor archive is ready`,
                        link: { label: t`Download` },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: t`Couldn't download vendor archive`,
                        link: { label: t`Try again` },
                        message: t`Please try again later.`,
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        case AsyncActionType.VENDOR_PROSPECTIVE_DOWNLOAD_REPORT_GENERATED: {
            switch (eventType) {
                case AsyncEventType.INITIATE: {
                    return {
                        title: t`The prospective vendor archive is being processed`,
                        message: t`We're preparing your prospective vendor archive and will notify you when it's ready.`,
                        severity: 'success',
                    };
                }
                case AsyncEventType.COMPLETE: {
                    return {
                        title: t`Your prospective vendor archive is ready`,
                        link: { label: t`Download` },
                        message: '',
                        severity: 'success',
                    };
                }
                case AsyncEventType.FAIL: {
                    return {
                        title: t`Couldn't download prospective vendor archive`,
                        link: { label: t`Try again` },
                        message: t`Please try again later.`,
                        severity: 'critical',
                    };
                }
                default: {
                    return null;
                }
            }
        }

        default: {
            return null;
        }
    }
};

export { downloadActionsType, getDownloadTypeDescriptor };
