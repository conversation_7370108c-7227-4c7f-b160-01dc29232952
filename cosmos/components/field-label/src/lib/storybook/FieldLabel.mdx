import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FieldLabelStories from './FieldLabel.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={FieldLabelStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>


<Primary />

<Controls of={FieldLabelStories.Playground} />

## Import

```jsx
import { FieldLabel } from '@drata/cosmos-field-label';
```

## 🟢 When to use the component

- **Building form field components** - Use as the label foundation when creating new Cosmos form components
- **Form field composition** - Integrate with other internal components like FieldFeedback to build complete form fields
- **Accessibility compliance** - Provide proper label-input associations in custom form components


## ❌ When not to use the component

- **Standard form fields** - Use complete form field components instead
- **Non-form contexts** - Use [Text](https://cosmos.drata.com/?path=/docs/typography-content-text--docs) component for headings, descriptions, or other non-label text

## 🛠️ How it works

The FieldLabel component serves as the foundational labeling system for all Cosmos form components. It creates accessible form labels using either `<label>` or `<legend>` HTML elements and is designed to be composed with input components and FieldFeedback to create complete form fields.

### Usability

#### Usage guidelines

When building new form components, use FieldLabel to ensure consistent labeling patterns across the design system:

- Integrate with FormField component for complete form field composition
- Follow the established prop patterns for label, help text, and optional indicators
- Ensure proper accessibility through htmlFor and labelId associations
- Use renderAsLegend for grouped form controls like radio button sets

Instead of using FieldLabel directly, use the complete form field components that already include proper labeling.

#### Using props

**Required props**
- **htmlFor:** The `id` of the input this label describes - creates proper label-input association
- **label:** The main label text that describes the expected input
- **labelId:** Unique `id` for the label element, used for ARIA relationships

**Content props**
- **helpText:** Optional supplementary text providing additional guidance (requires `helpTextId`)
- **helpTextId:** Required `id` for help text when `helpText` is provided
- **optionalText:** Text indicating the field is optional (e.g., "optional")

**Styling props**
- **size:** Label text size - `'sm'` (default), `'md'`, or `'lg'`
- **type:** Label text style - `'title'` (default, bold) or `'regular'` (normal weight)
- **gridArea:** CSS Grid area name for positioning in grid layouts

**Behavior props**
- **shouldHideLabel:** Visually hides the label while keeping it accessible to screen readers
- **renderAsLegend:** Renders as `<legend>` instead of `<label>` for fieldset groupings
- **cosmosUseWithCaution_hideLabelFromScreenReaders:** Hides label from screen readers (rarely needed)

**Size and type combinations:**
- **size:** `'sm'` (default), `'md'`, or `'lg'` - controls the Text component size used for the label
- **type:** `'title'` (default, bold) or `'regular'` (normal weight) - controls the Text component type used for the label

### Content

**Writing effective labels:**
- Avoid redundant words like "please enter" or "input your"
- Use clear, concise language that describes the expected input
- Avoid technical jargon unless your users are technical
- Use sentence case rather than title case for better readability
- Always provide `optionalText` when a field is optional

**Help text content:**
- Provide format examples for structured data (phone numbers, dates, etc.)
- Explain any validation rules or requirements
- Clarify ambiguous terms or concepts

### Accessibility

**What the design system provides:**
- Semantic `<label>` or `<legend>` HTML elements for proper screen reader support
- Proper `htmlFor` attribute association between labels and form controls
- ARIA-compliant help text association using `aria-describedby`
- Visually hidden label option that maintains screen reader accessibility
- High contrast text that meets WCAG AA standards
- Scalable text that works with browser zoom up to 200%

**Development responsibilities:**
- Always provide unique `htmlFor` values that match input `id` attributes
- Include `helpTextId` in the input's `aria-describedby` when help text is present
- Test label-input associations with screen readers
- Ensure form validation errors are properly associated with labels
- Use `renderAsLegend` appropriately for grouped form controls
- Avoid using `cosmosUseWithCaution_hideLabelFromScreenReaders` unless absolutely necessary

**Design responsibilities:**
- Ensure sufficient color contrast between label text and backgrounds
- Design clear visual hierarchy between labels, help text, and optional indicators
- Consider cognitive accessibility by keeping labels simple and scannable
- Test label readability across different screen sizes and orientations
- Provide consistent spacing between labels and their associated form controls
- Design error states that work clearly with label styling

**Screen reader behavior:**
- Labels are announced when users focus on associated form controls
- Help text is read after the label when properly associated with `aria-describedby`
- Optional text is included in the label announcement
- Legend elements are announced when entering fieldset groups
- Visually hidden labels are still announced normally to screen reader users

