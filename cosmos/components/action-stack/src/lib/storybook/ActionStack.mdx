import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as ActionStackStories from './ActionStack.stories';

<Meta of={ActionStackStories} />

<Title />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={ActionStackStories.Playground} />

## Import

```jsx
import { ActionStack } from '@cosmos/components/action-stack';
```
## Examples

### Simple ActionStack

To show a single grouping of actions, use the `actions` prop.

<Canvas of={ActionStackStories.Simple} />

### Multiple Stacks

To show multiple groups of actions, use the `stacks` prop.

<Canvas of={ActionStackStories.MultipleStacks} />

## 🟢 When to use the component

- **Multiple actions:** Use ActionStack to present users with related actions in a compact, easy-to-navigate format.
- **Action horizontal alignment** is ideal for situations where a horizontal arrangement of actions is preferable, such as tables, forms, modal dialogs, or page headers.
- **Related action grouping:** When you have actions that logically belong together and should be visually grouped for better user understanding.
- **Consistent spacing:** When you need uniform spacing and alignment between multiple interactive elements.
- **Responsive action layouts:** When actions need to adapt to different screen sizes while maintaining proper hierarchy and spacing.

## ❌ When not to use the component

- **Single Action:** If there is only one action to present, using ActionStack is unnecessary. Instead, use a standalone button.
- **Navigation menus:** Use dedicated navigation components for primary site navigation rather than ActionStack.
- **Unrelated actions:** Don't group actions that serve completely different purposes or contexts.

## 🛠️ How it works

### Usability

**Layout behavior:**
- Automatically handles spacing between actions using the `gap` prop
- Supports full-width layout or constrained maximum width
- Responsive behavior adapts to container constraints
- Maintains consistent alignment and spacing across different action types

**Stack configuration:**
- Each stack can have independent `shouldFlex`, `shouldWrap`, and `alignment` properties
- Stacks are displayed from left to right and top to bottom in the order provided
- Individual stacks can be configured for different responsive behaviors

**Keyboard navigation:**
- **Tab** - Navigate between actionable elements in the stack
- **Enter/Space** - Activate focused buttons or controls
- **Arrow keys** - Navigate within dropdown or select components
- **Escape** - Close open dropdowns or cancel interactions

#### Usage guidelines

- Use ActionStack when you have multiple related actions that belong together
- Supports various action types including buttons, dropdowns, and search
- Provides automatic responsive layout and consistent spacing between actions
- Can handle both simple action arrays and complex multi-stack configurations
- Use a minimum of one button to a maximum of three buttons.
- If there are more than three actions, group the least important actions under a "more" tertiary button.
- Don't add more than one primary button.
- It can be used left or right-aligned. Check [button alignment documentation](https://cosmos.drata.com/?path=/docs/actions-button--docs#order-alignment-and-grouping).

#### Using props

**Simple actions**
- **Definition:** Use the `actions` prop for a straightforward group of actions displayed left to right
- **Purpose:** When you have related actions without special grouping requirements
- **Use cases:** Form actions, table row actions, simple toolbars

**Multiple stacks**
- **Definition:** Use the `stacks` prop for multiple groups of actions with different alignment or behavior
- **Purpose:** When you need separate groups of actions with different positioning or wrapping behavior
- **Use cases:** Page headers with left and right action groups, complex toolbars with grouped functionality

**Action types supported:**
- **Button actions** - Standard button components with various levels and styles
- **Tooltip button actions** - Buttons with integrated tooltip functionality
- **Dropdown actions** - Dropdown menus for grouped secondary actions
- **Search actions** - Integrated search input components
- **Text actions** - Static text elements within the action flow
- **Custom components** - Custom React elements using `cosmosUseWithCaution_customComponent`

### Content

**Action organization:**
- Order actions by importance and frequency of use
- Group related actions together within the same stack
- Use parallel structure for similar action types
- Keep action labels concise and action-oriented

**Button hierarchy:**
- Use sentence case for all action labels
- Limit to one primary action per stack to maintain clear hierarchy
- Group secondary actions logically
- Use tertiary buttons or dropdowns for less important actions

**Action labeling:**
- Use action verbs that clearly describe what will happen
- Keep labels short and descriptive
- Avoid articles (a, an, the) in action labels when possible
- Maintain consistent terminology across related actions

### Accessibility

**What the design system provides:**
- Proper semantic markup for all action types
- Keyboard navigation support for all interactive elements
- Focus management within and between action stacks
- Screen reader announcements for dynamic actions
- ARIA attributes for complex action types like dropdowns
- High contrast mode compatibility for all action states
- Touch target sizing meets accessibility guidelines

**Development responsibilities:**
- Ensure proper labeling for icon-only buttons using `aria-label` or tooltip text
- Provide accessible error feedback for failed actions
- Test keyboard navigation across all action types
- Implement proper loading states for asynchronous actions
- Associate help text or context with complex action groups

**Design responsibilities:**
- Design clear visual hierarchy between primary, secondary, and tertiary actions
- Ensure sufficient contrast ratios for all action states
- Create accessible disabled and loading states
- Don't rely solely on color to convey action importance or state
- Maintain adequate spacing between actions for easy interaction


