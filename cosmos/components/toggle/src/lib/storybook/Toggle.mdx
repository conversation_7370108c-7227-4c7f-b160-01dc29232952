import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as ToggleStories from './Toggle.stories';

<Meta of={ToggleStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components. If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Primary />

<Controls of={ToggleStories.Playground} />

## Import

```jsx
import { Toggle } from '@drata/cosmos-toggle';
```

## Props

### `defaultChecked`

The `defaultChecked` prop defines the initial `checked` state of the component.

<Canvas of={ToggleStories.DefaultChecked} />

## 🟢 When to use the component

- **Building form field components** - Use as the foundation when creating toggle-based form components like [ToggleField](https://cosmos.drata.com/?path=/docs/forms-togglefield--docs)
- **Switch functionality** - When you need a low-level switch component for binary state management
- **Component composition** - Integrating with other internal components like FormField to build complete form experiences
- **Custom implementations only** - Only use directly when building custom components that need raw toggle functionality

## ❌ When not to use the component

- **Never use directly** - This is a private component intended only for building other Cosmos components
- **Standard form toggles** - Use [ToggleField](https://cosmos.drata.com/?path=/docs/forms-togglefield--docs) for complete form toggle functionality with labels and validation
- **Checkbox alternatives** - Use [Checkbox](https://cosmos.drata.com/?path=/docs/forms-checkbox--docs) or [CheckboxField](https://cosmos.drata.com/?path=/docs/forms-checkboxfield--docs) for multi-select scenarios
- **Multiple option selection** - Use [ToggleGroup](https://cosmos.drata.com/?path=/docs/forms-togglegroup--docs) or [RadioFieldGroup](https://cosmos.drata.com/?path=/docs/forms-radiofieldgroup--docs) for selecting between multiple options

## 🛠️ How it works

The Toggle component provides the foundational switch functionality for Cosmos form components. It's built on Radix Primitives Switch and handles the core toggle behavior, state management, and accessibility features that other components can build upon.

### Usability

- **State management** - Toggle handles both controlled and uncontrolled state patterns through `checked` and `defaultChecked` props
- **Form integration** - Provides proper `name`, `value`, and `onChange` props for form submission
- **Accessibility foundation** - Includes ARIA attributes and keyboard support that higher-level components inherit
- **Visual consistency** - Maintains consistent toggle appearance across all Cosmos form components
- **Immediate feedback** - Toggle state changes should be immediately visible and understandable

### Content

- **Proper labeling** - Always associate Toggle with appropriate labels through `aria-labelledby` or `aria-describedby`
- **Form context** - Provide meaningful `name` and `value` props for form submission
- **State feedback** - Use `onChange` to handle state changes and provide user feedback
- **Validation integration** - Connect with validation systems through parent form field components


### Accessibility

This component is built using the [Radix Primitives Switch](https://www.radix-ui.com/primitives/docs/components/switch), and follows the [Switch WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/switch/).

**What the design system provides:**
- Proper ARIA attributes for switch role and state
- Keyboard navigation support (Space and Enter keys)
- Focus management and visual focus indicators
- Screen reader announcements for state changes
- High contrast support for visual accessibility

**Development responsibilities:**
- Provide proper `aria-labelledby` or `aria-describedby` associations
- Ensure toggle has accessible labeling context
- Test keyboard navigation in component compositions
- Verify screen reader announcements work correctly
- Use ToggleField for complete accessibility implementation in applications
- Ensure toggle purpose is clear from surrounding content
- Test with assistive technologies in real form contexts

**Design responsibilities:**
- Design clear visual states for on/off positions
- Ensure sufficient color contrast for all toggle states
- Design focus indicators that meet accessibility standards
- Consider toggle placement and spacing for touch targets
- Design labels and context that clearly communicate toggle purpose

#### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <td>
                <kbd>Space</kbd>
            </td>
            <td>Checks/unchecks the toggle</td>
        </tr>
        <tr>
            <td>
                <kbd>Enter</kbd>
            </td>
            <td>Checks/unchecks the toggle</td>
        </tr>
    </tbody>
</table>

