import { Meta } from '@storybook/addon-docs/blocks';
import LinkTo from '@storybook/addon-links/react';

<Meta title="Documentation TODO" />

# Component Documentation TODO

## What Needs to be Done

Each component below needs content written for the scaffolded sections.

**Focus areas for content completion:**
- 📝 Technical examples (Storybook stories)
- 📝 "When to use" guidelines
- 📝 "When not to use" guidelines
- 📝 Usability guidelines
- 📝 Content guidelines
- 📝 Accessibility guidelines
- 📝 Link to Figma

## Components Needing Documentation

**When completed, change ⬜️ to ✅.**

- ⬜️ <LinkTo kind="Components/Accordion" story="docs">Accordion</LinkTo>
- ✅ <LinkTo kind="Components/ActionStack" story="docs">ActionStack</LinkTo>
- ⬜️ <LinkTo kind="Components/AttachedFile" story="docs">AttachedFile</LinkTo>
- ✅ <LinkTo kind="Components/AvatarStack" story="docs">AvatarStack</LinkTo>
- ⬜️ <LinkTo kind="Components/Box" story="docs">Box</LinkTo>
- ⬜️ <LinkTo kind="Components/Breadcrumbs" story="docs">Breadcrumbs</LinkTo>
- ⬜️ <LinkTo kind="Components/Callout" story="docs">Callout</LinkTo>
- ⬜️ <LinkTo kind="Components/Card" story="docs">Card</LinkTo>
- ✅ <LinkTo kind="Components/Checkbox" story="docs">Checkbox</LinkTo>
- ⬜️ <LinkTo kind="Components/CheckboxField" story="docs">CheckboxField</LinkTo>
- ⬜️ <LinkTo kind="Components/CheckboxFieldGroup" story="docs">CheckboxFieldGroup</LinkTo>
- ✅ <LinkTo kind="Components/ChoiceCard" story="docs">ChoiceCard</LinkTo>
- ✅ <LinkTo kind="Components/ChoiceCardGroup" story="docs">ChoiceCardGroup</LinkTo>
- ⬜️ <LinkTo kind="Components/CodeViewer" story="docs">CodeViewer</LinkTo>
- ⬜️ <LinkTo kind="Components/Combobox" story="docs">Combobox</LinkTo>
- ⬜️ <LinkTo kind="Components/ComboboxField" story="docs">ComboboxField</LinkTo>
- ⬜️ <LinkTo kind="Components/Confirmation" story="docs">Confirmation</LinkTo>
- ⬜️ <LinkTo kind="Components/CopyField" story="docs">CopyField</LinkTo>
- ✅ <LinkTo kind="Components/DataDonut" story="docs">DataDonut</LinkTo>
- ✅ <LinkTo kind="Components/DataGauge" story="docs">DataGauge</LinkTo>
- ✅ <LinkTo kind="Components/DataHeatmap" story="docs">DataHeatmap</LinkTo>
- ✅ <LinkTo kind="Components/DataLegendCosmosLab" story="docs">DataLegendCosmosLab</LinkTo>
- ✅ <LinkTo kind="Components/DataMeter" story="docs">DataMeter</LinkTo>
- ✅ <LinkTo kind="Components/DataPosture" story="docs">DataPosture</LinkTo>
- ⬜️ <LinkTo kind="Components/Datatable" story="docs">Datatable</LinkTo> **Note:** 4 separate documentation files (Datatable, Columns, Search, TableActions)
- ⬜️ <LinkTo kind="Components/DateRangeField" story="docs">DateRangeField</LinkTo>
- ⬜️ <LinkTo kind="Components/DateTime" story="docs">DateTime</LinkTo>
- ✅ <LinkTo kind="Components/Divider" story="docs">Divider</LinkTo>
- ✅ <LinkTo kind="Components/EmptyValue" story="docs">EmptyValue</LinkTo>
- ⬜️ <LinkTo kind="Components/Feedback" story="docs">Feedback</LinkTo>
- ⬜️ <LinkTo kind="Components/FieldFeedback" story="docs">FieldFeedback</LinkTo>
- ✅ <LinkTo kind="Components/FieldLabel" story="docs">FieldLabel</LinkTo>
- ⬜️ <LinkTo kind="Components/FileUploadField" story="docs">FileUploadField</LinkTo>
- ⬜️ <LinkTo kind="Components/FocusScopeCosmos" story="docs">FocusScopeCosmos</LinkTo>
- ⬜️ <LinkTo kind="UI Forms/Form" story="docs">Form</LinkTo>
- ⬜️ <LinkTo kind="Components/FormField" story="docs">FormField</LinkTo>
- ✅ <LinkTo kind="Components/FrameworkBadge" story="docs">FrameworkBadge</LinkTo>
- ✅ <LinkTo kind="Components/FrameworkBadgeStack" story="docs">FrameworkBadgeStack</LinkTo>
- ✅ <LinkTo kind="Components/GalleryCard" story="docs">GalleryCard</LinkTo>
- ⬜️ <LinkTo kind="Components/GalleryLayout" story="docs">GalleryLayout</LinkTo>
- ⬜️ <LinkTo kind="Components/Grid" story="docs">Grid</LinkTo>
- ⬜️ <LinkTo kind="Components/Private/HeatmapBox" story="docs">HeatmapBox</LinkTo>
- ⬜️ <LinkTo kind="Components/Icon" story="docs">Icon</LinkTo>
- ⬜️ <LinkTo kind="Components/Identity" story="docs">Identity</LinkTo>
- ⬜️ <LinkTo kind="Components/ImageUploadField" story="docs">ImageUploadField</LinkTo>
- ⬜️ <LinkTo kind="Components/Input" story="docs">Input</LinkTo>
- ⬜️ <LinkTo kind="Components/InterstitialLayout" story="docs">InterstitialLayout</LinkTo>
- ⬜️ <LinkTo kind="Components/List" story="docs">List</LinkTo>
- ⬜️ <LinkTo kind="Private/ListBox" story="docs">ListBox</LinkTo>
- ⬜️ <LinkTo kind="Components/Modal" story="docs">Modal</LinkTo>
- ⬜️ <LinkTo kind="Components/NavigationMenuCosmosLab" story="docs">NavigationMenuCosmosLab</LinkTo>
- ✅ <LinkTo kind="Components/Organization" story="docs">Organization</LinkTo>
- ✅ <LinkTo kind="Components/OrganizationStack" story="docs">OrganizationStack</LinkTo>
- ⬜️ <LinkTo kind="Components/PageHeader" story="docs">PageHeader</LinkTo>
- ✅ <LinkTo kind="Components/PaginationControls" story="docs">PaginationControls</LinkTo>
- ⬜️ <LinkTo kind="Components/Panel" story="docs">Panel</LinkTo>
- ⬜️ <LinkTo kind="Components/PanelSection" story="docs">PanelSection</LinkTo>
- ⬜️ <LinkTo kind="Components/PdfViewerCosmosLab" story="docs">PdfViewerCosmosLab</LinkTo>
- ⬜️ <LinkTo kind="Components/Popover" story="docs">Popover</LinkTo>
- ⬜️ <LinkTo kind="Components/RadioField" story="docs">RadioField</LinkTo>
- ⬜️ <LinkTo kind="Components/RadioFieldGroup" story="docs">RadioFieldGroup</LinkTo>
- ⬜️ <LinkTo kind="Components/RiskScore" story="docs">RiskScore</LinkTo>
- ⬜️ <LinkTo kind="Components/Search" story="docs">Search</LinkTo>
- ⬜️ <LinkTo kind="Components/Select" story="docs">Select</LinkTo>
- ⬜️ <LinkTo kind="Components/SelectField" story="docs">SelectField</LinkTo>
- ⬜️ <LinkTo kind="Components/ShowMore" story="docs">ShowMore</LinkTo>
- ⬜️ <LinkTo kind="Components/SimpleTable" story="docs">SimpleTable</LinkTo>
- ⬜️ <LinkTo kind="Components/Skeleton" story="docs">Skeleton</LinkTo>
- ⬜️ <LinkTo kind="Components/Slider" story="docs">Slider</LinkTo>
- ⬜️ <LinkTo kind="Components/SliderField" story="docs">SliderField</LinkTo>
- ⬜️ <LinkTo kind="Components/Snackbar" story="docs">Snackbar</LinkTo>
- ⬜️ <LinkTo kind="Components/SpotIllustration" story="docs">SpotIllustration</LinkTo>
- ✅ <LinkTo kind="Components/StatBlock" story="docs">StatBlock</LinkTo>
- ✅ <LinkTo kind="Components/StatusDot" story="docs">StatusDot</LinkTo>
- ⬜️ <LinkTo kind="Components/Tabs" story="docs">Tabs</LinkTo>
- ⬜️ <LinkTo kind="Components/TagGroup" story="docs">TagGroup</LinkTo>
- ⬜️ <LinkTo kind="Components/Text" story="docs">Text</LinkTo>
- ⬜️ <LinkTo kind="Components/TextEditorCosmosLab" story="docs">TextEditorCosmosLab</LinkTo>
- ⬜️ <LinkTo kind="Components/TextField" story="docs">TextField</LinkTo>
- ⬜️ <LinkTo kind="Components/TextareaField" story="docs">TextareaField</LinkTo>
- ⬜️ <LinkTo kind="Components/Threshold" story="docs">Threshold</LinkTo>
- ✅ <LinkTo kind="Components/Toggle" story="docs">Toggle</LinkTo>
- ⬜️ <LinkTo kind="Components/ToggleField" story="docs">ToggleField</LinkTo>
- ⬜️ <LinkTo kind="Components/ToggleGroup" story="docs">ToggleGroup</LinkTo>
- ✅ <LinkTo kind="Components/Truncation" story="docs">Truncation</LinkTo>
- ⬜️ <LinkTo kind="Components/Wizard" story="docs">Wizard</LinkTo>
- ⬜️ <LinkTo kind="Components/WizardStep" story="docs">WizardStep</LinkTo>
- ⬜️ <LinkTo kind="Components/Wordmark" story="docs">Wordmark</LinkTo>
---

## Summary

- **Total Components:** 99
- **Complete:** 20 components (20%)
- **Need Documentation:** 79 components (80%)

### Complete Components
ActionStack, AvatarStack, Checkbox, ChoiceCard, ChoiceCardGroup, DataDonut, DataGauge, DataHeatmap, DataLegendCosmosLab, DataMeter, DataPosture, Divider, EmptyValue, FieldLabel, FrameworkBadge, FrameworkBadgeStack, Organization, OrganizationStack, PaginationControls, StatBlock

### Libraries
- Cosmos: 49 components
- Cosmos Lab: 41 components
- UI Forms: 1 component

## Next Steps
1. Choose a component from the list above
2. Fill in the scaffolded content sections
3. Follow existing complete components as examples
4. Focus on user guidance over technical implementation
