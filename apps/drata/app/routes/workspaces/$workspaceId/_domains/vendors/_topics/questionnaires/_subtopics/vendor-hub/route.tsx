import { ActionStack } from '@cosmos/components/action-stack';
import { Organization } from '@cosmos-lab/components/organization';
import type { MetaFunction } from '@remix-run/node';
import {
    getVendorHubQuestionnaireInProgressHeaderActions,
    VendorHubQuestionnaireView,
} from '@views/vendor-hub-questionnaire';

export const meta: MetaFunction = () => [{ title: 'Vendor Questionnaire' }];

const isQuestionnaireCompleted = false;

export const handle = {
    overrides: {
        pageHeader: {
            pageId: 'vendor-hub-questionnaire-page',
            actionStack: (
                <ActionStack
                    data-id="vendor-hub-questionnaire-action-stack"
                    stacks={[
                        {
                            actions:
                                getVendorHubQuestionnaireInProgressHeaderActions() ??
                                [],
                            id: 'vendor-hub-questionnaire-action-stack',
                        },
                    ]}
                />
            ),
            slot: (
                <Organization
                    imgSrc="https://drata.com/images/favicon-32x32.png"
                    imgAlt="vendor logo"
                />
            ),
        },
    },
};
const VendorHubQuestionnaire = (): React.JSX.Element => {
    return (
        <VendorHubQuestionnaireView
            isQuestionnaireCompleted={isQuestionnaireCompleted}
            data-testid="VendorHubQuestionnaire"
            data-id="Emjyng8H"
        />
    );
};

export default VendorHubQuestionnaire;
