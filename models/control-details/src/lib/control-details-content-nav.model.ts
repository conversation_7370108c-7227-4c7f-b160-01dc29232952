import {
    sharedControlDetailsController,
    sharedControlsDetailsStatsController,
} from '@controllers/controls';
import { sharedMonitoringDetailsControlsController } from '@controllers/monitoring-details';
import type { Tab } from '@controllers/route';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class ControlDetailsContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get showRisksTab(): boolean {
        const { isRiskManagerWithRestrictedView, hasRiskReadPermission } =
            sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;
        const { isWorkspaceAdministrator } = sharedCurrentUserController;

        return (
            Boolean(currentWorkspace?.primary) &&
            (hasRiskReadPermission || isRiskManagerWithRestrictedView) &&
            !isWorkspaceAdministrator
        );
    }

    get tabs(): Tab[] {
        const { controlId, controlDetails } = sharedControlDetailsController;
        const { monitoringDetailsControlsTotal } =
            sharedMonitoringDetailsControlsController;
        const {
            controlEvidenceStats: { notReady: controlEvidenceNotReadyStats },
            controlPoliciesStats: { notReady: controlPoliciesNotReadyStats },
            controlMonitorsStats: { notReady: controlMonitorsNotReadyStats },
        } = sharedControlsDetailsStatsController;
        const { isCustomWorkflowsEnabled, isMapControlsTestsEnabled } =
            sharedFeatureAccessModel;

        if (!controlId) {
            return [];
        }

        return [
            {
                topicPath: `compliance/controls/${controlId}/overview`,
                label: t`Overview`,
            },
            {
                topicPath: `compliance/controls/${controlId}/evidence`,
                label: t`Evidence`,
                ...(controlEvidenceNotReadyStats > 0 && {
                    metadata: {
                        label: controlEvidenceNotReadyStats.toString(),
                        colorScheme: 'critical',
                        type: 'number',
                    },
                }),
            },
            ...(controlDetails?.isMonitored ||
            isMapControlsTestsEnabled ||
            monitoringDetailsControlsTotal > 0
                ? [
                      {
                          topicPath: `compliance/controls/${controlId}/monitoring`,
                          label: t`Monitoring`,
                          ...(controlMonitorsNotReadyStats > 0 && {
                              metadata: {
                                  label: controlMonitorsNotReadyStats.toString(),
                                  colorScheme: 'critical',
                                  type: 'number',
                              } as const,
                          }),
                      },
                  ]
                : []),
            {
                topicPath: `compliance/controls/${controlId}/policies`,
                label: t`Policies`,
                ...(controlPoliciesNotReadyStats > 0 && {
                    metadata: {
                        label: controlPoliciesNotReadyStats.toString(),
                        colorScheme: 'critical',
                        type: 'number',
                    },
                }),
            },
            {
                topicPath: `compliance/controls/${controlId}/frameworks`,
                label: t`Frameworks`,
            },
            ...(this.showRisksTab
                ? [
                      {
                          topicPath: `compliance/controls/${controlId}/risks`,
                          label: t`Risks`,
                      },
                  ]
                : []),
            ...(isCustomWorkflowsEnabled
                ? [
                      {
                          topicPath: `compliance/controls/${controlId}/workflows`,
                          label: t`Workflows`,
                      },
                  ]
                : []),
        ];
    }
}
