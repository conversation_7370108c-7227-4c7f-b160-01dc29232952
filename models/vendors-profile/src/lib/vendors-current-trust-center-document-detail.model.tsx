import { sharedVendorsCurrentTrustCenterDocumentDetailController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { makeAutoObservable } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { AppLink } from '@ui/app-link';
import {
    SECTION_QUERY_PARAM,
    SECTIONS,
} from '@views/vendors-profile-trust-center';
import { VENDORS_TRUST_CENTER_DOCUMENT_DETAIL_ACTIONS } from './constants/vendor-current-trust-center-document-detail.constants';

export class VendorsCurrentTrustCenterDocumentDetailModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return (
            sharedVendorsCurrentTrustCenterDocumentDetailController.fileName ||
            ''
        );
    }

    get backLink(): React.JSX.Element {
        const parentHref = new URL(getParentRoute(window.location.href, 2))
            .pathname;

        return (
            <AppLink
                data-id="vendors-trust-center-document-back-link"
                href={`${parentHref}?${SECTION_QUERY_PARAM}=${SECTIONS.DOCUMENTS}`}
                label="Back"
            />
        );
    }

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="vendors-trust-center-document-detail-page-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions: VENDORS_TRUST_CENTER_DOCUMENT_DETAIL_ACTIONS,
                        id: 'vendors-trust-center-document-detail-actions-stack',
                    },
                ]}
            />
        );
    }
}
