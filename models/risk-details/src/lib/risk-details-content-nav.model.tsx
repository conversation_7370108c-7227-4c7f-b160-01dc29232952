import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { Tab } from '@controllers/route';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { NavigationItem } from '@ui/page-content';

export class RiskDetailsContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!riskDetails || !currentWorkspaceId) {
            return [];
        }

        const { riskId } = riskDetails;

        return [
            {
                id: 'overview',
                topicPath: `risk/register/management/${riskId}/overview`,
                label: 'Overview',
            },
            {
                id: 'mitigating-controls',
                topicPath: `risk/register/management/${riskId}/mitigating-controls`,
                label: 'Mitigating controls',
            },
        ];
    }

    get config(): NavigationItem[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!riskDetails || !currentWorkspaceId) {
            return [];
        }

        const { riskId } = riskDetails;

        return [
            {
                id: 'overview',
                props: {
                    label: 'Overview',
                    href: `/workspaces/${currentWorkspaceId}/risk/register/management/${riskId}/overview`,
                },
            },
            {
                id: 'mitigating-controls',
                props: {
                    label: 'Mitigating controls',
                    href: `/workspaces/${currentWorkspaceId}/risk/register/management/${riskId}/mitigating-controls`,
                },
            },
        ];
    }
}
