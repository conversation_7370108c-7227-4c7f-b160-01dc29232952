import { capitalize, noop } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import { getRiskTreatmentLabel } from '@helpers/risk-treatment';
import { calculateRiskMetrics } from '@views/risk-register-overview';

export class RiskDetailsContentHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return `${sharedRiskDetailsController.riskDetails?.title}`;
    }

    get slot(): React.JSX.Element {
        return (
            <Metadata
                colorScheme="neutral"
                label={`${sharedRiskDetailsController.riskDetails?.riskId}`}
                type="tag"
            />
        );
    }

    get breadcrumbs(): Breadcrumb[] {
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return [];
        }

        return [
            {
                label: 'Register',
                pathname: `/workspaces/${currentWorkspaceId}/risk/register`,
            },
        ];
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;

        const inheritMetrics = calculateRiskMetrics(
            riskDetails?.score ?? 0,
            riskSettings,
        );

        const residualMetrics = calculateRiskMetrics(
            riskDetails?.residualScore ?? 0,
            riskSettings,
        );

        return [
            {
                iconName: 'Edit',
                iconSize: '100',
                ariaLabel: 'Edit treatment',
                onClick: noop,
                id: 'treatment',
                'data-id': 'treatment',
                label: 'Treatment',
                value: riskDetails?.treatmentPlan
                    ? getRiskTreatmentLabel(riskDetails.treatmentPlan)
                    : '—',
                type: 'TEXT',
            },
            {
                iconName: 'Edit',
                iconSize: '100',
                id: 'score',
                'data-id': 'score',
                label: 'Score',
                value: (
                    <RiskScore
                        intensity="moderate"
                        severity={inheritMetrics.severity}
                        scoreNumber={riskDetails?.score}
                        label={capitalize(inheritMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
                ariaLabel: 'Edit score',
                onClick: noop,
            },
            {
                iconName: 'Edit',
                iconSize: '100',
                id: 'residual-score',
                'data-id': 'residual-score',
                label: 'Residual score',
                value: (
                    <RiskScore
                        intensity="moderate"
                        severity={residualMetrics.severity}
                        scoreNumber={riskDetails?.residualScore}
                        label={capitalize(residualMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
                ariaLabel: 'Edit residual score',
                onClick: noop,
            },
            {
                iconName: 'Edit',
                iconSize: '100',
                id: 'owner',
                'data-id': 'owner',
                label: 'Owner',
                value: riskDetails?.owners.map((owner) => {
                    const { firstName, lastName, id, avatarUrl } = owner;
                    const fullName = getFullName(firstName, lastName);
                    const fallbackText = getInitials(fullName);

                    return (
                        <AvatarIdentity
                            key={id}
                            primaryLabel={fullName}
                            fallbackText={fallbackText}
                            imgSrc={avatarUrl}
                            data-id="xTMIBGty"
                        />
                    );
                }),
                type: 'REACT_NODE',
                ariaLabel: 'Edit owner',
                onClick: noop,
            },
        ];
    }
}
