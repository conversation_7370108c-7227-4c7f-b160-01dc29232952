import { isNil } from 'lodash-es';
import type { ObjectItem } from '@components/object-selector';
import { sharedPoliciesLibraryInfiniteListController } from '@controllers/policies';
import type { PolicyTableResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export const MAP_POLICIES_MODAL_ID = 'map-policies-modal';

class MapPoliciesModel {
    selectedPolicies: PolicyTableResponseDto[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    isPolicyPublished(policy: PolicyTableResponseDto): boolean {
        return !isNil(policy.version) && !isNil(policy.version.publishedAt);
    }

    get selectedPoliciesAsObjectItems(): ObjectItem<PolicyTableResponseDto>[] {
        return this.selectedPolicies.map((policy) => ({
            id: String(policy.id),
            value: String(policy.id),
            label: policy.name,
            description: `Policy ID: ${policy.id}`,
            objectType: 'POLICY' as const,
            objectData: policy,
            avatar: {
                fallbackText: policy.name.charAt(0).toUpperCase(),
                imgAlt: t`Policy`,
            },
        }));
    }

    getCurrentSelectedPolicies() {
        const { policiesListAsItems, mapPolicyToItem } =
            sharedPoliciesLibraryInfiniteListController;

        return this.selectedPolicies.map((policy) => {
            const fullPolicyItem = policiesListAsItems.find(
                (option) => String(policy.id) === option.id,
            );

            if (!fullPolicyItem) {
                return mapPolicyToItem(policy);
            }

            return fullPolicyItem;
        });
    }

    clearSelectedPolicies() {
        this.selectedPolicies = [];
    }
}

export const sharedMapPoliciesModel = new MapPoliciesModel();
