/* eslint-disable tsdoc/syntax -- these comments were taken for a third party documentation */
import type { Remarks } from './remarks.type';

/**
 * An attribute, characteristic, or quality of the
 * containing object expressed as a namespace
 * qualified name/value pair. The value of a
 * property is a simple scalar value, which may be
 * expressed as a list of values.
 */
export interface Property {
    /**
     * A textual label that uniquely identifies a
     * specific attribute, characteristic, or quality of
     * the property's containing object.
     * STRING PATTERN: ^[_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-\.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$.
     */
    name: string;
    /**
     * A unique identifier that can be used to reference
     * this property elsewhere in an OSCAL document. A
     * UUID should be consistently used for a given
     * location across revisions of the document.
     * STRING PATTERN: ^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-4[0-9A-Fa-f]{3}-[89ABab][0-9A-Fa-f]{3}-[0-9A-Fa-f]{12}$.
     */
    uuid?: string;
    /**
     * A namespace qualifying the property's name. This
     * allows different organizations to associate
     * distinct semantics with the same name.
     */
    ns?: string;
    /**
     * Indicates the value of the attribute,
     * characteristic, or quality.
     * STRING PATTERN: ^\S(.*\S)?$.
     */
    value: string;
    /**
     * A textual label that provides a sub-type or
     * characterization of the property's name. This can
     * be used to further distinguish or discriminate
     * between the semantics of multiple properties of
     * the same object with the same name and ns.
     * STRING PATTERN: ^[_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-\.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$.
     */
    class?: string;
    remarks?: Remarks;
}
