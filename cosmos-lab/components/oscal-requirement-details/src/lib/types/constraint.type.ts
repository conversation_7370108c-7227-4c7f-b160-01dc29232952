/* eslint-disable tsdoc/syntax -- these comments were taken for a third party documentation */
/**
 * A formal or informal expression of a constraint
 * or test.
 */
export interface Constraint {
    /**
     * A textual summary of the constraint to be applied.
     */
    description?: string;
    tests?: {
        /**
         * A formal (executable) expression of a constraint
         * STRING PATTERN: ^\S(.*\S)?$.
         */
        [key: string]: unknown;
    }[];
}
