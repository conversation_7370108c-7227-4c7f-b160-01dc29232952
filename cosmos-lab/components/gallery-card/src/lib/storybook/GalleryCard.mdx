import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as GalleryCardStories from './GalleryCard.stories';

<Meta of={GalleryCardStories} />

<Title />

<Description />

<Primary />

<Controls of={GalleryCardStories.Playground} />

## Import

```jsx
import { GalleryCard } from '@cosmos-lab/components/gallery-card-cosmos-lab';
```

## 🟢 When to use the component

- **Gallery layouts** - Display collections of items in a grid format where each item has multiple pieces of information
- **Visual content cards** - Items that benefit from having an image, avatar, or badge as visual identification
- **Status-aware content** - Items that need status indicators or data visualizations

## ❌ When not to use the component

- **Tabular data** - Use [Datatable](https://cosmos.drata.com/?path=/docs/information-data-datatable--docs) for data that needs sorting, filtering, or column-based comparison
- **Dense information display** - Use tables or lists when you need to show many items compactly without visual hierarchy
- **Inconsistent content structure** - When items in the collection have vastly different information types or layouts

## 🛠️ How it works

The GalleryCard component creates a structured layout with distinct areas for images, titles, metadata, data visualizations, and actions. It enforces accessibility standards and provides consistent spacing and visual hierarchy across gallery interfaces.

### Usability

#### Using props

**Required props:**
- **title:** The main heading that identifies the item

**Content props:**
- **imageSlot:** Visual identifier like Avatar, FrameworkBadge, or custom imagery
- **optionalText:** Secondary descriptive text below the title
- **metadataSlot:** Content area for displaying key information using KeyValuePair components or other metadata elements (If using KeyValuePair, you can include up to 3 as metadata and they must use a label and value)
- **datavizSlot:** Data visualization components
- **statusStack:** Status indicators using Metadata components
- **middleInfo:** Additional content between metadata and actions
- **actionStack:** Array of action stacks containing specific buttons or links for user interaction

#### Layout behavior

**Two-column layout:**
- When `datavizSlot` has content, it displays in a two-column grid alongside `metadataSlot`
- Data visualization appears on the left, metadata on the right
- Maintains responsive behavior on smaller screens

**Single-column layout:**
- When `datavizSlot` is empty, only `metadataSlot` is rendered and spans the full width
- Provides more space for metadata content when no data visualization is present

### Content

**Metadata best practices:**
- **Keep metadata concise** - Focus on the most important information that helps users identify and understand the item
- **Prioritize scannable content** - Choose metadata that users can quickly scan to make decisions

**Action requirements:**
- **No clickable cards** - The entire card should not be interactive; users must click specific action elements
- **Clear action labels** - Use descriptive button text that clearly indicates what will happen (e.g., "Open audit", "View details", "Start process")

### Accessibility

**What the design system provides:**
- Semantic `<article>` element for proper content structure
- Proper heading hierarchy with title as the main heading
- Keyboard navigation support for all interactive elements
- Screen reader accessible action buttons with descriptive labels
- High contrast borders and text that meet WCAG AA standards

**Development responsibilities:**
- Provide descriptive `title` text that identifies the item clearly
- Use meaningful action button labels that describe the specific action
- Ensure `imageSlot` content includes proper alt text when using images
- Associate help text or descriptions with form elements if the card contains inputs
- Test keyboard navigation through all interactive elements

**Design responsibilities:**
- Maintain consistent visual hierarchy between title, metadata, and actions
- Ensure sufficient color contrast for all text and interactive elements
- Design clear visual separation between different content areas
- Provide consistent spacing and alignment across gallery layouts
- Consider mobile responsiveness for touch targets and content reflow

