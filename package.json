{"name": "@multiverse/source", "type": "module", "version": "0.0.0", "license": "MIT", "scripts": {"   [App Commands]   ": "", "app:drata:dev": "pnpm run tokens && pnpm run i18n:prepare && concurrently \"remix vite:dev ./apps/drata --config ./apps/drata/vite.config.ts --open --strictPort\"  \"pnpm run i18n:watch\"", "app:drata:build": "pnpm run i18n:prepare && remix vite:build ./apps/drata --config ./apps/drata/vite.config.ts && node ./scripts/extract-inline-scripts.mjs apps/drata/dist/client", "app:drata:preview": "vite preview ./apps/drata --open", "app:drata:analyze": "ANALYZE=1 pnpm run app:drata:build && open ./apps/drata/dist/client/stats.html", "   [Internationalization]  ": "", "i18n:extract": "lingui extract", "i18n:watch": "pnpm run i18n:extract --clean --overwrite --watch --debounce=100", "i18n:compile": "lingui compile --typescript", "i18n:prepare": "rm -rf ./globals/i18n/messages && pnpm run i18n:extract --clean --overwrite && pnpm run i18n:compile", "   [Generators]   ": "", "generate": "node --experimental-strip-types ./generators/module.ts", "generate:config": "node --experimental-strip-types ./generators/config.ts", "generate:component": "pnpm run generate --type=component", "generate:controller": "pnpm run generate --type=controller", "generate:global": "pnpm run generate --type=global", "generate:helper": "pnpm run generate --type=helper", "generate:model": "pnpm run generate --type=model", "generate:ui": "pnpm run generate --type=ui", "generate:view": "pnpm run generate --type=view", "generate:cosmos-lab": "node --experimental-strip-types ./generators/cosmos-lab.ts", "generate:all-routes": "cd apps/drata && npx remix routes --json > routes-output.json && cd ../.. && node scripts/create-all-routes.js", "   [Auto-generated Code]  ": "", "update-api-sdk": "openapi-ts --file ./globals/api-sdk/src/openapi-ts.config.ts && node ./globals/api-sdk/src/post-generation-fixes.mjs", "tokens": "node ./cosmos/constants/tokens/src/lib/scripts/build.cjs && biome format --vcs-enabled=false --write cosmos/constants/tokens/src/build/docs", "   [Helpers]   ": "", "qs": "pnpm install && pnpm update-api-sdk && pnpm run tokens && pnpm run app:drata:dev", "format": "biome format --write .", "update-ts-config": "node --experimental-strip-types ./generators/tsconfig-paths.ts", "clean-up": "pnpm run update-ts-config", "   [Quality Checks]   ": "", "test": "vitest", "typecheck": "tsc --project tsconfig.cli.json --noEmit", "lint": "eslint . --ignore-pattern '**/scripts' --ignore-pattern 'cosmos/constants/tokens/**/*.cjs'", "   [Storybook]  ": "", "storybook": "pnpm run tokens && storybook dev -p 6006", "storybook:build": "pnpm run tokens && storybook build --output-dir=dist/storybook/storybook", "   [Repo Maintenance]   ": "", "prepare": "husky", "update-dependencies": "taze major", "update-dependencies-mvp": "node scripts/update-dependencies-mvp.mjs", "update-dependencies-safe": "node scripts/update-dependencies-mvp.mjs --dry-run", "update-dependencies-minor": "node scripts/update-dependencies-mvp.mjs", "cleanup-dependency-backups": "node scripts/cleanup-dependency-backups.mjs"}, "private": true, "packageManager": "pnpm@10.12.4", "dependencies": {"@apideck/vault-js": "1.8.0", "@ckeditor/ckeditor5-react": "9.5.0", "@datadog/browser-logs": "6.13.0", "@datadog/browser-rum": "6.13.0", "@drata/design-system": "1.8.1", "@drata/enums": "0.0.134", "@drata/recurring-schedule": "0.0.10", "@flatfile/api": "^1.17.5", "@flatfile/hooks": "^1.6.0", "@flatfile/listener": "^1.1.2", "@flatfile/plugin-record-hook": "^2.0.2", "@flatfile/react": "^7.13.10", "@floating-ui/react-dom": "2.1.4", "@hey-api/client-fetch": "0.13.1", "@hookform/resolvers": "4.1.3", "@internationalized/date": "3.8.2", "@lingui/core": "5.3.2", "@lingui/macro": "5.3.2", "@lingui/react": "5.3.2", "@monaco-editor/react": "4.7.0", "@mui/base": "5.0.0-beta.70", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@radix-ui/themes": "3.2.1", "@remix-run/node": "2.16.8", "@remix-run/react": "2.16.8", "@remix-run/serve": "2.16.8", "@segment/analytics-next": "1.81.0", "@semcore/ui": "16.4.1", "@tanstack/query-core": "5.81.5", "@tanstack/react-query": "5.81.5", "@tanstack/react-table": "8.21.3", "@typeform/embed": "5.5.0", "ckeditor5": "45.2.1", "ckeditor5-premium-features": "45.2.1", "classnames": "2.5.1", "dompurify": "^3.2.6", "downshift": "9.0.9", "formik": "2.4.6", "isbot": "5.1.28", "js-cookie": "3.0.5", "launchdarkly-js-client-sdk": "3.8.1", "lodash-es": "4.17.21", "markdown-to-jsx": "7.7.10", "mobx": "6.13.7", "mobx-react": "9.2.0", "mobx-utils": "6.1.1", "nanoid": "5.1.5", "pusher-js": "8.4.0", "qs": "6.14.0", "react": "18.3.1", "react-aria": "3.41.1", "react-aria-components": "1.10.1", "react-beautiful-dnd": "13.1.1", "react-dom": "18.3.1", "react-dropzone": "14.3.8", "react-hook-form": "7.59.0", "react-stately": "3.39.0", "recharts": "2.15.4", "styled-components": "6.1.19", "weakref": "0.2.1", "zod": "3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@eslint-community/eslint-plugin-eslint-comments": "4.5.0", "@eslint/js": "9.29.0", "@faker-js/faker": "9.9.0", "@hey-api/openapi-ts": "0.76.0", "@lingui/babel-plugin-lingui-macro": "5.3.2", "@lingui/cli": "5.3.2", "@lingui/vite-plugin": "5.3.2", "@playwright/test": "1.53.1", "@remix-run/dev": "2.16.8", "@storybook/addon-a11y": "9.0.15", "@storybook/addon-docs": "9.0.15", "@storybook/addon-links": "9.0.15", "@storybook/addon-themes": "9.0.15", "@storybook/react-vite": "9.0.15", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/babel__core": "7.20.5", "@types/estree": "1.0.8", "@types/estree-jsx": "1.0.5", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "22.15.30", "@types/qs": "6.14.0", "@types/react": "18.3.18", "@types/react-beautiful-dnd": "13.1.8", "@types/react-dom": "18.3.1", "@typescript-eslint/parser": "8.35.0", "@typescript-eslint/utils": "8.35.0", "@vitejs/plugin-react": "4.6.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "3.2.4", "concurrently": "9.2.0", "eslint": "9.29.0", "eslint-config-sheriff": "28.0.0", "eslint-import-resolver-typescript": "4.4.3", "eslint-interactive": "12.0.0", "eslint-plugin-lingui": "0.10.1", "eslint-plugin-mobx": "0.0.13", "eslint-plugin-playwright": "2.2.0", "eslint-plugin-storybook": "9.0.12", "happy-dom": "18.0.1", "husky": "9.1.7", "isbot": "5.1.28", "jsdom": "26.1.0", "lint-staged": "16.1.2", "pusher-js": "8.4.0", "remark-gfm": "4.0.1", "remix-flat-routes": "^0.8.5", "rollup-plugin-visualizer": "6.0.3", "storybook": "9.0.15", "style-dictionary": "3.8.0", "taze": "19.1.0", "type-fest": "4.41.0", "typescript": "5.8.3", "typescript-eslint": "8.35.0", "vite": "6.3.5", "vite-plugin-devtools-json": "0.2.1", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4"}, "resolutions": {"@typescript-eslint/parser": "8.35.0", "@typescript-eslint/utils": "8.35.0", "eslint-import-resolver-typescript": "4.4.3", "eslint-plugin-storybook": "9.0.12"}}