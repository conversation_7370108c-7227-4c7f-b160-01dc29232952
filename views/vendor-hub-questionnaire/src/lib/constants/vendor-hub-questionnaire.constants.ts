import { noop } from 'lodash-es';
import type { ComponentProps } from 'react';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';

export const VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY =
    'vendor-hub-questionnaire-header';

export const getVendorHubQuestionnaireCompletedHeaderActions =
    (): ComponentProps<typeof ActionStack>['actions'] => [
        {
            id: `${VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY}-completion-date`,
            actionType: 'text',
            typeProps: { children: t`Completed on [date time]` },
        },
    ];

export const getVendorHubQuestionnaireInProgressHeaderActions =
    (): ComponentProps<typeof ActionStack>['actions'] => [
        {
            actionType: 'button',
            id: `${VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY}-submit-button`,

            typeProps: {
                'data-id': `${VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY}-submit-button`,
                label: t`Complete later`,
                onClick: noop,
                type: 'submit',
                level: 'tertiary',
            },
        },
        {
            actionType: 'button',
            id: `${VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY}-complete-button`,

            typeProps: {
                'data-id': `${VENDOR_HUB_QUESTIONNAIRE_HEADER_KEY}-complete-button`,
                label: t`Complete questionnaire`,
                onClick: noop,
                type: 'button',
            },
        },
    ];
