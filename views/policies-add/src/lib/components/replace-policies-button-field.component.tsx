import { noop } from 'lodash-es';
import type { ObjectItem } from '@components/object-selector';
import { openPolicyWithSLASelector } from '@components/policies';
import { Button } from '@cosmos/components/button';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { ActivePoliciesDataWithSlaResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { CustomFieldRenderProps } from '@ui/forms';

/**
 * Custom field component that replaces the policies dropdown with a button
 * that opens the global policy selector modal.
 */
export const ReplacePoliciesButtonField = (
    props: CustomFieldRenderProps,
): React.JSX.Element => {
    const { value = [], setValue, onBlur, error, 'data-id': dataId } = props;
    const selectedPoliciesCount = Array.isArray(value) ? value.length : 0;
    const hasSelectedPolicies = selectedPoliciesCount > 0;

    const handleSubmit = action(
        (
            selectedItems:
                | ObjectItem<ActivePoliciesDataWithSlaResponseDto>[]
                | ObjectItem<ActivePoliciesDataWithSlaResponseDto>,
        ) => {
            // Handle both single and multi-select responses
            const items = Array.isArray(selectedItems)
                ? selectedItems
                : [selectedItems];

            // Extract policy IDs from the selected items
            const policyIds = items.map((item) => item.objectData.id);

            setValue(policyIds);
        },
    );

    const handleOpenModal = action(() => {
        openPolicyWithSLASelector({
            config: {
                selectionMode: 'multi',
                modal: {
                    id: 'replace-policies',
                    title: t`Select Policies to Replace`,
                    size: 'lg',
                    confirmButtonLabel: t`Select`,
                    cancelButtonLabel: t`Cancel`,
                    showSelectedCount: true,
                },
                search: {
                    placeholder: t`Search by policy name...`,
                    label: t`Search policies`,
                    loaderLabel: t`Loading policies...`,
                    emptyStateMessage: t`No policies found matching your search criteria.`,
                    clearAllLabel: t`Clear all`,
                },
                filters: {
                    // Exclude already selected policies if needed
                    excludeIds: Array.isArray(value) ? value.map(String) : [],
                },
            },
            callbacks: {
                onSelected: handleSubmit,
                onCancel: noop,
            },
        });
    });

    const getButtonLabel = () => {
        if (hasSelectedPolicies) {
            return t`Edit selected policies`;
        }

        return t`Select policies to replace`;
    };

    const getSelectedPoliciesText = () => {
        if (selectedPoliciesCount === 1) {
            return t`1 policy selected`;
        }

        const policiesText = t`policies selected`;

        return `${selectedPoliciesCount} ${policiesText}`;
    };

    const handleResetSelections = action(() => {
        setValue([]);
    });

    return (
        <Stack
            direction="column"
            gap="sm"
            data-testid="ReplacePoliciesButtonField"
            data-id={dataId}
        >
            <Button
                label={getButtonLabel()}
                level="secondary"
                size="md"
                data-id={`${dataId}-button`}
                onClick={handleOpenModal}
                onBlur={onBlur}
            />

            {hasSelectedPolicies && (
                <Stack direction="row" gap="md" align="center">
                    <Text
                        size="200"
                        colorScheme="neutral"
                        data-id={`${dataId}-selected-count`}
                    >
                        {getSelectedPoliciesText()}
                    </Text>
                    <Button
                        data-id={`${dataId}-reset-button`}
                        label={t`Reset selections`}
                        level="tertiary"
                        size="sm"
                        onClick={handleResetSelections}
                    />
                </Stack>
            )}

            {error?.message && (
                <Feedback
                    title={error.message}
                    severity="critical"
                    data-id={`${dataId}-error`}
                />
            )}
        </Stack>
    );
};
