import { isNil } from 'lodash-es';
import {
    sharedControlDetailsController,
    sharedControlPoliciesController,
    sharedControlsDownloadController,
} from '@controllers/controls';
import { modalController } from '@controllers/modal';
import type { TableAction } from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import { sharedResetPoliciesModalModel } from '@models/controls';
import { PoliciesCompareToDefaultsModal } from '../components';
import { openMapPoliciesModal } from '../helpers/map-policies-modal.helper';

class ControlsPoliciesViewModel {
    constructor() {
        makeAutoObservable(this);
    }

    get displayCompareToDefaults(): boolean {
        const { hasControlTemplatePermission } = sharedFeatureAccessModel;
        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );

        return hasControlTemplatePermission && isTemplatedControl;
    }

    handleDownloadPolicies = (): void => {
        const { controlId } = sharedControlPoliciesController;
        const { downloadControlPolicies } = sharedControlsDownloadController;

        if (controlId) {
            runInAction(() => {
                downloadControlPolicies(controlId);
            });
        }
    };

    get tableActions(): TableAction[] {
        const { total } = sharedControlPoliciesController;
        const hasPolicies = total > 0;

        return [
            ...(hasPolicies
                ? [
                      {
                          actionType: 'button',
                          id: 'download-button',
                          typeProps: {
                              startIconName: 'Download',
                              level: 'tertiary',
                              colorScheme: 'neutral',
                              label: t`Download`,
                              isLoading:
                                  sharedControlsDownloadController.isDownloadControlPoliciesLoading,
                              onClick: this.handleDownloadPolicies,
                          },
                      } satisfies TableAction,
                  ]
                : []),
            ...(this.displayCompareToDefaults
                ? [
                      {
                          actionType: 'button',
                          id: 'compare-to-defaults-button',
                          typeProps: {
                              level: 'tertiary',
                              label: t`Compare to defaults`,
                              onClick: () => {
                                  const { controlId } =
                                      sharedControlPoliciesController;

                                  if (controlId) {
                                      sharedResetPoliciesModalModel.loadPolicyComparison(
                                          controlId,
                                      );
                                  }

                                  modalController.openModal({
                                      id: 'policy-compare-to-defaults-modal',
                                      size: 'lg',
                                      content: () => (
                                          <PoliciesCompareToDefaultsModal data-id="O78iVRjN" />
                                      ),
                                  });
                              },
                          },
                      } satisfies TableAction,
                  ]
                : []),
            {
                actionType: 'button',
                id: 'map-policies-button',
                typeProps: {
                    level: 'secondary',
                    label: t`Map policies`,
                    onClick: openMapPoliciesModal,
                },
            },
        ];
    }
}

export const sharedControlsPoliciesViewModel = new ControlsPoliciesViewModel();
