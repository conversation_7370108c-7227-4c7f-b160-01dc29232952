import { isEmpty } from 'lodash-es';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { observer } from '@globals/mobx';
import { FrameworksOverviewContent } from './components/frameworks-overview-content';
import { FrameworksOverviewOscalContent } from './components/frameworks-overview-oscal-content';

export const FrameworksOverviewView = observer((): React.JSX.Element => {
    const { requirement } = sharedRequirementDetailsController;

    const shouldRenderOscalOverview = !isEmpty(requirement?.parts);

    if (shouldRenderOscalOverview) {
        return <FrameworksOverviewOscalContent />;
    }

    return <FrameworksOverviewContent data-id="0-vKgSLJ" />;
});
