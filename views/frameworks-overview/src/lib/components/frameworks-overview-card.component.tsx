import { TextBreakLine } from '@components/text-break-line';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { frameworksAdditionalInfo2DescriptionHeader } from '../helpers/frameworks-additional-info-2-description-header.helper';
import { frameworksAdditionalInfo3DescriptionHeader } from '../helpers/frameworks-additional-info-3-description-header.helper';
import { frameworksAdditionalInfoDescriptionHeader } from '../helpers/frameworks-additional-info-description-header.helper';
import { frameworksDescriptionHeader } from '../helpers/frameworks-description-header.helper';

export const FrameworksOverviewViewCard = observer((): React.JSX.Element => {
    const { requirement, isRequirementLoading } =
        sharedRequirementDetailsController;
    const descriptionTitle = frameworksDescriptionHeader();
    const additionalInfoDescriptionTitle =
        frameworksAdditionalInfoDescriptionHeader();
    const additionalInfo2DescriptionTitle =
        frameworksAdditionalInfo2DescriptionHeader();
    const additionalInfo3DescriptionTitle =
        frameworksAdditionalInfo3DescriptionHeader();

    return (
        <Grid
            gap={'xl'}
            data-testid="FrameworksOverviewViewCard"
            data-id="MnpB3GLy"
        >
            <KeyValuePair
                label={t`Code`}
                type={isRequirementLoading ? 'REACT_NODE' : 'TEXT'}
                value={isRequirementLoading ? <Skeleton /> : requirement?.name}
            />
            <KeyValuePair
                label={t`Control description`}
                type={'REACT_NODE'}
                value={
                    isRequirementLoading ? (
                        <Skeleton />
                    ) : (
                        <TextBreakLine text={requirement?.description ?? ''} />
                    )
                }
            />
            {requirement?.longDescription && (
                <KeyValuePair
                    label={descriptionTitle}
                    type="REACT_NODE"
                    value={
                        isRequirementLoading ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine text={requirement.longDescription} />
                        )
                    }
                />
            )}
            {requirement?.additionalInfo && (
                <KeyValuePair
                    label={additionalInfoDescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isRequirementLoading ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine text={requirement.additionalInfo} />
                        )
                    }
                />
            )}
            {requirement?.additionalInfo2 && (
                <KeyValuePair
                    label={additionalInfo2DescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isRequirementLoading ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine text={requirement.additionalInfo2} />
                        )
                    }
                />
            )}
            {requirement?.additionalInfo3 && (
                <KeyValuePair
                    label={additionalInfo3DescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isRequirementLoading ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine text={requirement.additionalInfo3} />
                        )
                    }
                />
            )}
        </Grid>
    );
});
