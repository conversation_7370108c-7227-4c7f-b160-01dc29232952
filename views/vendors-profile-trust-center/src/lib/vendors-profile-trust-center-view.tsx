import { sharedVendorsDetailsController } from '@controllers/vendors';
import { Banner } from '@cosmos/components/banner';
import { But<PERSON> } from '@cosmos/components/button';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useScrollIntoView } from '@globals/use-scroll-into-view';
import { VendorsProfileTrustCenterComplianceCard } from './components/vendors-profile-trust-center-compliance-card';
import { VendorsProfileTrustCenterDocumentsCard } from './components/vendors-profile-trust-center-documents-card';
import { VendorsProfileTrustCenterIntroCard } from './components/vendors-profile-trust-center-intro-card';
import { VendorsProfileTrustCenterItemsCard } from './components/vendors-profile-trust-center-items-card';
import { VendorsProfileTrustCenterSubprocessorsCard } from './components/vendors-profile-trust-center-subprocessors-card';
import {
    SECTION_QUERY_PARAM,
    SECTIONS,
} from './constants/vendor-trust-center-sections.constants';
import { useVendorTrustCenterRedirect } from './hooks/useVendorTrustCenterRedirect.hook';

export const VendorsProfileTrustCenterView = observer(
    (): React.JSX.Element | null => {
        const { vendorDetails, isLoading } = sharedVendorsDetailsController;
        const { shouldRedirect } = useVendorTrustCenterRedirect();
        const scrollToDocumentsRef = useScrollIntoView(
            SECTION_QUERY_PARAM,
            SECTIONS.DOCUMENTS,
        );

        if (shouldRedirect) {
            return null;
        }

        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        const vendorName = vendorDetails?.name ?? '';
        const bannerTitle = t`You are viewing public information that ${vendorName} has made available to everyone.`;

        return (
            <Stack
                gap="4x"
                direction="column"
                data-testid="VendorsProfileTrustCenterView"
                data-id="7OOIy2GJ"
            >
                <Banner
                    title={bannerTitle}
                    severity="primary"
                    displayMode="full"
                    body={
                        <Stack direction="column" align="start" gap="4x">
                            <Text colorScheme="primary">
                                <Trans>
                                    Make a request to access their documents and
                                    other information about their security
                                    posture.
                                </Trans>
                            </Text>
                            <Button
                                label={t`Request access`}
                                level="primary"
                                startIconName="Lock"
                                size="md"
                            />
                        </Stack>
                    }
                />
                <Stack gap="6x" direction="column">
                    <VendorsProfileTrustCenterIntroCard />
                    <VendorsProfileTrustCenterComplianceCard />
                    <div ref={scrollToDocumentsRef}>
                        <VendorsProfileTrustCenterDocumentsCard />
                    </div>
                    <VendorsProfileTrustCenterSubprocessorsCard />
                    <VendorsProfileTrustCenterItemsCard />
                </Stack>
            </Stack>
        );
    },
);
