import {
    VendorInternalDetailsFormComponent,
    VendorInternalDetailsInfoComponent,
} from '@components/vendors-current-add-vendor';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';

interface VendorInternalDetailsCardComponentProps {
    isEditable?: boolean;
}

export const VendorInternalDetailsCardComponent = observer(
    ({
        isEditable = true,
    }: VendorInternalDetailsCardComponentProps): React.JSX.Element => {
        const {
            vendorDetails,
            updateInternalDetails,
            isUpdatingVendor,
            hasErrorVendorUpdate,
        } = sharedVendorsDetailsController;
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleOnSubmit = (values: FormValues) => {
            vendorDetails &&
                updateInternalDetails(vendorDetails.id, vendorDetails, values);
        };

        return (
            <ViewEditCardComponent
                title={t`Internal Details`}
                data-testid="VendorInternalDetailsCardComponent"
                data-id="u7zczS_t"
                isMutationPending={toJS(isUpdatingVendor)}
                hasMutationError={toJS(hasErrorVendorUpdate)}
                readOnlyComponent={
                    <VendorInternalDetailsInfoComponent state={vendorDetails} />
                }
                editComponent={
                    isEditable ? (
                        <VendorInternalDetailsFormComponent
                            formId={'internal-details-form'}
                            handleOnSubmit={handleOnSubmit}
                            ref={formRef}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        );
    },
);
