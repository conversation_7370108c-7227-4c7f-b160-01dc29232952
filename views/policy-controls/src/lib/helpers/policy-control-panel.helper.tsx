import { ControlPanel } from '@components/controls';
import { sharedControlDetailsOrchestratorController } from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { action } from '@globals/mobx';

export const handleOpenEvidenceControlPanel = action(
    ({ controlId }: { controlId: number }) => {
        sharedControlDetailsOrchestratorController.load(controlId);
        panelController.openPanel({
            id: 'policy-control-panel',
            content: () => (
                <ControlPanel controlSource="POLICY" data-id="95a12CTy" />
            ),
        });
    },
);
