import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import { sharedControlsController } from '@controllers/controls';
import {
    sharedCompareToDefaultMappedControlsModalController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import {
    sharedRequirementAssociateControlsMutationController,
    sharedRequirementDetailsController,
} from '@controllers/requirements';
import type { TableAction } from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { openCompareToDefaultMappedControlsModal } from '../helpers/open-compare-to-default-mapped-requirements-modal.helper';

export class FrameworksControlsActionsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tableActions(): TableAction[] {
        const { frameworkDetails } = sharedFrameworkDetailsController;
        const { requirement } = sharedRequirementDetailsController;

        const isCustomFramework = frameworkDetails?.tag === 'CUSTOM';
        const actions: TableAction[] = [];

        const { hasWriteFrameworkPermission, hasControlTemplatePermission } =
            sharedFeatureAccessModel;

        if (
            !isCustomFramework &&
            frameworkDetails &&
            hasControlTemplatePermission
        ) {
            if (requirement) {
                sharedCompareToDefaultMappedControlsModalController.setRequirementId(
                    requirement.id,
                );
            }

            actions.push({
                actionType: 'button',
                id: 'compare-to-defaults-action',
                typeProps: {
                    label: t`Compare to defaults`,
                    level: 'tertiary',
                    onClick: openCompareToDefaultMappedControlsModal,
                },
            });
        }

        if (hasWriteFrameworkPermission) {
            actions.push({
                actionType: 'button',
                id: 'map-controls-action-stack',
                typeProps: {
                    label: t`Map controls`,
                    level: 'secondary',
                    onClick: this.handleOpenModal,
                },
            });
        }

        return actions;
    }

    handleConfirm = (controlIds: number[]): void => {
        const { handleAssociateControls } =
            sharedRequirementAssociateControlsMutationController;
        const { requirement } = sharedRequirementDetailsController;

        if (requirement) {
            handleAssociateControls(controlIds, requirement.id);
        }
    };

    handleOpenModal = (): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { controls } = sharedControlsController;

        if (currentWorkspace) {
            openLinkControlsModalWithWorkspace({
                objectType: 'risk',
                onConfirm: (selectedControls) => {
                    const controlIds = selectedControls.map(
                        (item) => item.controlData.id,
                    );

                    this.handleConfirm(controlIds as number[]);
                },
                excludeControlIds: controls.map((control) => control.id),
                workspaceId: currentWorkspace.id,
            });
        }
    };
}

export const sharedFrameworksControlsActionsModel =
    new FrameworksControlsActionsModel();
