import { AppDatatable } from '@components/app-datatable';
import { sharedMonitorsController } from '@controllers/monitors';
import { panelController } from '@controllers/panel';
import { action, observer } from '@globals/mobx';
import { MonitoringDetailsPanelComponent } from '../components/monitor-details-panel.component';
import { getControlsMonitoringColumns } from '../constants/columns.constant';
import { sharedControlsMonitoringActionsModel } from '../models/controls-monitoring-actions.model';
import { sharedMonitoringDetailsPanelModel } from '../models/monitoring-details-panel.model';

const openMonitoringPanel = action((testId: number): void => {
    sharedMonitoringDetailsPanelModel.loadPanelInfo(testId);

    panelController.openPanel({
        id: 'monitoring-panel',
        content: () => <MonitoringDetailsPanelComponent data-id="r4-3_HsY" />,
    });
});

export const ControlsMonitoringView = observer((): React.JSX.Element => {
    const { monitors, isLoading, monitorsTotal } = sharedMonitorsController;
    const { tableActions, emptyStateProps } =
        sharedControlsMonitoringActionsModel;

    return (
        <AppDatatable
            tableId="datatable-monitoring-list"
            data-testid="ControlsMonitoringView"
            data-id="cLwGEtjx"
            isLoading={isLoading}
            total={monitorsTotal}
            data={monitors}
            columns={getControlsMonitoringColumns()}
            tableActions={tableActions}
            emptyStateProps={emptyStateProps}
            onRowClick={({ row }) => {
                openMonitoringPanel(row.testId);
            }}
            onFetchData={(params) => {
                sharedMonitorsController.loadMonitors(undefined, params);
            }}
        />
    );
});
