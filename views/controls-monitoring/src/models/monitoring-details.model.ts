import {
    activeTicketsMetadataController,
    activeTrackCardController,
    sharedMonitorFindingsController,
    sharedMonitoringHistoryController,
} from '@controllers/monitoring-details';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import type { ColorScheme } from '@cosmos/components/metadata';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getMonths } from '../constants/months.constant';
import { getStatusLabelText } from '../helpers/get-status-label-text.helper';
import {
    getMonitorStatusMetadata,
    getStatusColorScheme,
    type StatusMetadata,
} from '../utils/monitor-status-metadata.util';

interface MonitoringHistory {
    month: string;
    Fail: number;
    Pass: number;
    Error: number;
}

class MonitoringDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get status(): StatusMetadata | undefined {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return undefined;
        }

        const { checkResultStatus } = workspaceMonitorTestOverview;

        return getMonitorStatusMetadata(checkResultStatus);
    }

    get statusColor(): ColorScheme {
        const { trackData } = activeTrackCardController;

        if (!trackData) {
            return 'neutral';
        }

        return getStatusColorScheme(trackData.status);
    }

    get statusLabel(): string {
        const { trackData } = activeTrackCardController;

        return getStatusLabelText(trackData?.status);
    }

    get resourcesFailingCount(): number {
        return sharedMonitorFindingsController.failingResources;
    }

    get resourcesFailingText(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        const total = workspaceMonitorTestOverview?.recipes.length ?? 0;

        return t`out of ${total} total`;
    }

    get sinceDateText(): string {
        const { trackData } = activeTrackCardController;

        if (!trackData?.startDateWithStatus) {
            return t`no date registred`;
        }

        const since = t`since`;

        return `${since} ${formatDate('field_time', trackData.startDateWithStatus)}`;
    }

    get numberOfDays(): number | string {
        const { trackData } = activeTrackCardController;

        if (!trackData) {
            return '—';
        }

        return trackData.consecutiveDays > 0 ? trackData.consecutiveDays : '—';
    }

    get ticketsInProgress(): number {
        return activeTicketsMetadataController.ticketsInProgress;
    }

    get totalTicketsText(): string {
        const ticketsTotal = activeTicketsMetadataController.totalTickets;

        return t`out of ${ticketsTotal} total`;
    }

    get history(): MonitoringHistory[] {
        const historyData =
            sharedMonitoringHistoryController.monitoringHistoryData;

        if (!historyData) {
            return [];
        }

        const monthsNumberic = {} as Record<string, number>;

        for (let i = 0; i < historyData.labels.length; i = i + 1) {
            monthsNumberic[historyData.labels[i]] = i;
        }

        return historyData.labels.map((monthData: string) => ({
            month: getMonths()[monthData],
            Fail: historyData.failed[monthsNumberic[monthData]],
            Pass: historyData.passed[monthsNumberic[monthData]],
            Error: historyData.errored[monthsNumberic[monthData]],
        }));
    }
}

export const sharedMonitoringDetailsModel = new MonitoringDetailsModel();
