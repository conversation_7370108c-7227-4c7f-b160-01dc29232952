import { isEmpty, isNil } from 'lodash-es';
import { useEffect, useMemo } from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import { WorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { providers } from '@globals/providers';
import type { ControlsMonitoringCellProps } from '../types/controls-monitoring-cell.type';

export const MonitorConnectionCell = observer(
    ({ row }: ControlsMonitoringCellProps): React.JSX.Element => {
        const { allConfiguredConnections } = sharedConnectionsController;
        const monitorOverviewController = useMemo(
            () => new WorkspaceMonitorsController(),
            [],
        );

        const {
            isOverviewLoading,
            loadWorkspaceMonitorTestOverview,
            workspaceMonitorTestOverview,
        } = monitorOverviewController;

        useEffect(() => {
            loadWorkspaceMonitorTestOverview(row.original.testId);
        }, [loadWorkspaceMonitorTestOverview, row.original.testId]);

        if (!workspaceMonitorTestOverview || isOverviewLoading) {
            return <EmptyValue label="—" />;
        }

        const { availableConnections } = workspaceMonitorTestOverview;

        if (!availableConnections || isEmpty(availableConnections)) {
            return <EmptyValue label="—" />;
        }

        const connectedClientTypes = allConfiguredConnections.map(
            (configureConnection) => configureConnection.clientType,
        );

        const availableClientTypes = availableConnections.flatMap(
            (availableConnection) => {
                if (
                    connectedClientTypes.includes(
                        availableConnection.clientType as ClientTypeEnum,
                    )
                ) {
                    return availableConnection;
                }

                return {
                    clientType: 'CUSTOM',
                };
            },
        );

        const components = availableClientTypes.flatMap((connection) => {
            const providerInfo = Object.values(providers).find(
                (p) => p.id === connection.clientType,
            );

            if (isNil(providerInfo)) {
                return (
                    <AvatarIdentity
                        data-id="Q3-DIXIE"
                        key={connection.clientType}
                        primaryLabel={connection.clientType}
                        imgSrc={providers.CUSTOM.logo}
                    />
                );
            }

            return (
                <AvatarIdentity
                    key={connection.clientType}
                    primaryLabel={providerInfo.name}
                    imgSrc={providerInfo.logo}
                    data-id="Q3-D_rtk"
                />
            );
        });

        return <>{components}</>;
    },
);
