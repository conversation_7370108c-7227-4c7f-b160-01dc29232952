import { type default as React, useEffect } from 'react';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';
import { VendorsSecurityReviewFilesComponent } from './components/vendors-security-review-files-component';
import {
    SECURITY_REVIEW_FILES_CELLS,
    SECURITY_REVIEW_QUESTIONNAIRES_CELLS,
    VENDORS_PROFILE_SECURITY_REVIEW_ADD_FILES_ACTIONS,
    VENDORS_PROFILE_SECURITY_REVIEW_ADD_QUESTIONNAIRE_ACTIONS,
} from './constants/vendors-profile-security-review.constants';

export const VendorsProfileSecurityReviewView = observer(
    (): React.JSX.Element => {
        const { workspaceId, vendorId, securityReviewId } = useParams();
        const navigate = useNavigate();

        const { files = [], questionnaires = [] } =
            sharedVendorsSecurityReviewDocumentsController;

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        useEffect(() => {
            // Redirect if security review is completed
            if (
                securityReviewDetails?.status === 'COMPLETED' &&
                !isSecurityReviewLoading
            ) {
                navigate(
                    `/workspaces/${workspaceId}/vendors/current/${vendorId}/security-reviews/${securityReviewId}/completed`,
                );
            }
        }, [
            securityReviewDetails?.status,
            isSecurityReviewLoading,
            navigate,
            workspaceId,
            vendorId,
            securityReviewId,
        ]);

        return (
            <Stack
                gap="xl"
                direction="column"
                data-testid="VendorsProfileSecurityReviewView"
                data-id="WwQyGcrv"
            >
                <Card
                    data-id="vendors-profile-security-review-add-files-card"
                    title="Files"
                    actions={VENDORS_PROFILE_SECURITY_REVIEW_ADD_FILES_ACTIONS}
                    body={
                        <VendorsSecurityReviewFilesComponent
                            data-testid="VendorsSecurityReviewFilesComponent"
                            data-id="WwQyGcrv"
                            data={files}
                            columns={SECURITY_REVIEW_FILES_CELLS}
                        />
                    }
                />
                <Card
                    data-id="vendors-profile-security-review-add-questionnaires-card"
                    title="Questionnaires"
                    body={
                        <VendorsSecurityReviewFilesComponent
                            data-testid="VendorsSecurityReviewQuestionnairesComponent"
                            data-id="WwQyGcrv"
                            data={questionnaires}
                            columns={SECURITY_REVIEW_QUESTIONNAIRES_CELLS}
                        />
                    }
                    actions={
                        VENDORS_PROFILE_SECURITY_REVIEW_ADD_QUESTIONNAIRE_ACTIONS
                    }
                />
            </Stack>
        );
    },
);
