import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { panelController } from '@controllers/panel';
import type { AuditHubCustomerRequestControlResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { openControlDetailsPanelPaginated } from '../helpers/open-control-details-panel.helper';

class ControlDetailsPanelController {
    constructor() {
        makeAutoObservable(this);
    }

    get currentControlId(): number | undefined {
        return panelController.queryParams.controlId as number | undefined;
    }

    get customerRequestControls(): AuditHubCustomerRequestControlResponseDto[] {
        return panelController.queryParams
            .controlsOfCurrentPage as AuditHubCustomerRequestControlResponseDto[];
    }

    get panelQueryParams(): Record<string, unknown> {
        return panelController.queryParams;
    }

    get currentControlIndex(): number {
        return this.customerRequestControls.findIndex(
            (control) => Number(control.id) === this.currentControlId,
        );
    }

    get totalControls(): number {
        return this.customerRequestControls.length;
    }

    get currentControlPosition(): number {
        return this.currentControlIndex + 1;
    }

    get currentControlData(): AuditHubCustomerRequestControlResponseDto {
        return this.customerRequestControls[this.currentControlIndex];
    }

    get hasValidControlData(): boolean {
        return Boolean(this.currentControlData);
    }

    loadControlEvidences = (): void => {
        if (!this.currentControlId) {
            return;
        }
        sharedCustomerRequestDetailsController.loadControlEvidences(
            this.currentControlId,
        );
    };

    get canNavigateNext(): boolean {
        return (
            this.currentControlIndex < this.customerRequestControls.length - 1
        );
    }

    get canNavigatePrev(): boolean {
        return this.currentControlIndex > 0;
    }

    handleClosePanel = (): void => {
        panelController.closePanel();
    };

    handleNextPage = (): void => {
        if (!this.canNavigateNext) {
            return;
        }

        const nextControl =
            this.customerRequestControls[this.currentControlIndex + 1];

        openControlDetailsPanelPaginated(
            Number(nextControl.id),
            1,
            this.panelQueryParams,
        );
    };

    handlePrevPage = (): void => {
        if (!this.canNavigatePrev) {
            return;
        }

        const prevControl =
            this.customerRequestControls[this.currentControlIndex - 1];

        openControlDetailsPanelPaginated(
            Number(prevControl.id),
            -1,
            this.panelQueryParams,
        );
    };
}

export const sharedControlDetailsPanelController =
    new ControlDetailsPanelController();
