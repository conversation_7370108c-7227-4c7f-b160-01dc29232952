import { sharedRiskMitigationControlsController } from '@controllers/risk-details';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';

export const ActionCell = observer(
    ({
        row: { original: control },
    }: {
        row: { original: RiskControlResponseDto };
    }): React.JSX.Element => {
        const { riskId } = useParams();

        const { canUnmapControls } = sharedRiskMitigationControlsController;

        const handleUnmapControl = () => {
            if (!canUnmapControls || !riskId) {
                return;
            }

            sharedRiskMitigationControlsController.handleUnmapControl(
                riskId,
                control,
            );
        };

        const dropdownItems = [
            {
                id: 'unmap-control-option',
                label: t`Unmap control`,
                type: 'item' as const,
                value: 'unlink',
                onClick: handleUnmapControl,
                disabled: !canUnmapControls,
                tooltip: canUnmapControls
                    ? undefined
                    : t`You don't have permission to unmap controls`,
            },
        ];

        return (
            <SchemaDropdown
                isIconOnly
                label={t`Actions`}
                level="tertiary"
                size="md"
                colorScheme="neutral"
                startIconName="Action"
                data-testid="ActionCell"
                data-id="xuanRTQW"
                items={dropdownItems}
            />
        );
    },
);
