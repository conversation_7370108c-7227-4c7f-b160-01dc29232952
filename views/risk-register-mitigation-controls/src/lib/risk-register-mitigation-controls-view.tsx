import { Banner } from '@cosmos/components/banner';
import { Box } from '@cosmos/components/box';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { RiskMitigationControlsTableComponent } from '../components/risk-mitigation-controls-table-component';

export const RiskRegisterMitigationControlsView = observer(
    (): React.JSX.Element => {
        const { isRiskManagementEnabled, isMapControlsTestsEnabled } =
            sharedEntitlementFlagController;

        // Check if user has basic risk management access
        if (isRiskManagementEnabled) {
            return (
                <Box
                    p="2xl"
                    data-testid="RiskRegisterMitigationControlsView"
                    data-id="2o6TiaAy"
                >
                    <Banner
                        title={t`Risk Management Required`}
                        body={t`Upgrade your plan to access risk management features and control mapping capabilities.`}
                        severity="warning"
                        displayMode="section"
                        data-id="risk-management-upgrade-banner"
                    />
                </Box>
            );
        }

        // Show limited functionality if MAP_CONTROLS_TESTS entitlement is missing
        if (!isMapControlsTestsEnabled) {
            return (
                <Box
                    p="2xl"
                    data-testid="RiskRegisterMitigationControlsView"
                    data-id="2o6TiaAy"
                >
                    <Banner
                        title={t`Limited Functionality`}
                        body={t`Control mapping features are not available in your current plan. Contact your administrator to upgrade.`}
                        severity="primary"
                        displayMode="section"
                        data-id="map-controls-limitation-banner"
                    />
                    <Box mt="xl">
                        <RiskMitigationControlsTableComponent />
                    </Box>
                </Box>
            );
        }

        // Full functionality available
        return (
            <Box
                p="2xl"
                data-testid="RiskRegisterMitigationControlsView"
                data-id="2o6TiaAy"
            >
                <RiskMitigationControlsTableComponent />
            </Box>
        );
    },
);
