import type { DatatableProps } from '@cosmos/components/datatable';
import {
    FrameworkBadge,
    getFrameworkBadge,
    type TagType,
} from '@cosmos-lab/components/framework-badge';
import type { RequirementWithControlsShortListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { RequirementActionCell } from '../lib/components/requirement-action-cell.component';
import { RequirementDonutCell } from '../lib/components/requirement-donut-cell.component';
import { RequirementStatusCell } from '../lib/components/requirement-status-cell.component';

export const getControlsFrameworksTableColumns = (
    showActionsColumn: boolean,
): DatatableProps<RequirementWithControlsShortListResponseDto>['columns'] => {
    return [
        ...(showActionsColumn
            ? [
                  {
                      id: 'button',
                      isActionColumn: true,
                      meta: {
                          shouldIgnoreRowClick: true,
                      },
                      cell: RequirementActionCell,
                  },
              ]
            : []),
        {
            accessorKey: 'frameworkTag',
            header: t`Framework`,
            id: 'FRAMEWORK_SLUG',
            enableSorting: true,
            cell: ({ row }): React.ReactNode => {
                const { frameworkTag } = row.original;

                return (
                    <FrameworkBadge
                        badgeName={getFrameworkBadge(frameworkTag as TagType)}
                        data-id="5TngHk05"
                    />
                );
            },
        },
        {
            accessorKey: 'name',
            header: t`Code`,
            id: 'REQUIREMENT_NAME',
            enableSorting: true,
        },
        {
            header: t`Requirement`,
            id: 'REQUIREMENT_DESCRIPTION',
            accessorKey: 'description',
            enableSorting: true,
        },
        {
            header: t`Controls`,
            id: 'controls',
            accessorKey: 'controlsReady',
            enableSorting: false,
            cell: RequirementDonutCell,
            minSize: COLUMN_SIZES.LARGE,
        },
        {
            header: t`Status`,
            id: 'REQUIREMENT_STATUS',
            enableSorting: true,
            accessorKey: 'id',
            cell: RequirementStatusCell,
        },
    ];
};
