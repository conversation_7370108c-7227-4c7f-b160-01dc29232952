import { isEmpty, isNil } from 'lodash-es';
import { Metadata } from '@cosmos/components/metadata';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RequirementWithControlsShortListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

interface RequirementStatusCellProps {
    row: { original: RequirementWithControlsShortListResponseDto };
}

export const RequirementStatusCell = ({
    row: { original: requirement },
}: RequirementStatusCellProps): React.JSX.Element => {
    const { controlsReady, archivedAt } = requirement;
    const isArchived = !isNil(archivedAt);

    if (isArchived) {
        return (
            <Metadata
                label={t`Out of scope`}
                iconName="OutOfScope"
                colorScheme="neutral"
            />
        );
    }

    if (isEmpty(controlsReady)) {
        return <EmptyValue label={t`No status available`} />;
    }

    const { isReady } = controlsReady[0];

    return (
        <Metadata
            label={isReady ? t`Ready` : t`Not ready`}
            iconName={isReady ? 'Check' : 'Cancel'}
            colorScheme={isReady ? 'success' : 'critical'}
            data-testid="RequirementStatusCell"
            data-id="ImEKrTF3"
        />
    );
};
