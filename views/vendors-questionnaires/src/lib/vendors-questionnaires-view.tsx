import { useEffect } from 'react';
import type { VendorQuestionnaireRowData } from '@components/vendor-questionnaires';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { Datatable } from '@cosmos/components/datatable';
import { Loader } from '@cosmos/components/loader';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { getVendorsQuestionnairesColumns } from './vendors-questionnaires-table-columns';
import { getVendorsQuestionnairesFilters } from './vendors-questionnaires-table-config';

export const VendorsQuestionnairesView = observer((): React.JSX.Element => {
    const { hasSecurityQuestionnaireReadPermission } = sharedFeatureAccessModel;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    // Check if user has permission to read security questionnaires
    useEffect(() => {
        if (!hasSecurityQuestionnaireReadPermission && currentWorkspace?.id) {
            // Redirect to current vendors if no read permission
            navigate(`/workspaces/${currentWorkspace.id}/vendors/current`);
        }
    }, [
        hasSecurityQuestionnaireReadPermission,
        currentWorkspace?.id,
        navigate,
    ]);

    // Show loader while redirecting
    if (!hasSecurityQuestionnaireReadPermission) {
        return <Loader label={t`Loading`} />;
    }

    const { allVendorsQuestionnaires, isLoading, total, loadQuestionnaires } =
        sharedVendorsTypeformQuestionnairesController;

    const handleRowClick = ({ row }: { row: VendorQuestionnaireRowData }) => {
        const { id } = row;

        if (currentWorkspace?.id && id) {
            navigate(
                `/workspaces/${currentWorkspace.id}/vendors/questionnaires/${id}`,
            );
        }
    };

    return (
        <Datatable
            isLoading={isLoading}
            tableId="datatable-vendors-questionnaires"
            data={allVendorsQuestionnaires}
            data-id="datatable-vendors-questionnaires-data-id"
            columns={getVendorsQuestionnairesColumns()}
            total={total}
            filterProps={getVendorsQuestionnairesFilters()}
            tableSearchProps={{
                placeholder: t`Search`,
                hideSearch: false,
                debounceDelay: 500,
                defaultValue: '',
            }}
            onRowClick={handleRowClick}
            onFetchData={loadQuestionnaires}
        />
    );
});
