import type { Action } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';

export const QUESTIONNAIRES_ADD_HEADER_PAGE_ID =
    'vendors-questionnaires-add-header';

export const getQuestionnairesAddHeaderActions = (
    isCreateMode = false,
): Action[] => {
    const dropdownItems = [];

    // Add items based on mode
    if (!isCreateMode) {
        dropdownItems.push(
            {
                id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-dropdown-item-0`,
                label: t`Copy`,
                type: 'item',
                value: 'COPY',
                align: 'end',
            },
            {
                id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-dropdown-item-1`,
                label: t`Download`,
                type: 'item',
                value: 'DOWNLOAD',
            },
            {
                id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-dropdown-item-3`,
                label: t`Delete`,
                type: 'item',
                value: 'DELETE',
                colorScheme: 'critical',
            },
        );
    }

    // Always add Import questions
    dropdownItems.push({
        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-dropdown-item-2`,
        label: t`Import questions`,
        type: 'item',
        value: 'IMPORT_QUESTIONS',
    });

    const dropdownAction = {
        actionType: 'dropdown' as const,
        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-dropdown`,
        typeProps: {
            label: t`Questionnaire options menu`,
            level: 'tertiary',
            isIconOnly: true,
            startIconName: 'HorizontalMenu',
            align: 'end',
            items: dropdownItems,
        },
    };

    const previewAction = {
        actionType: 'button' as const,
        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-button-preview`,
        typeProps: {
            label: t`Preview`,
            level: 'tertiary',
        },
    };

    // Return actions based on mode
    return (
        isCreateMode ? [dropdownAction] : [dropdownAction, previewAction]
    ) as Action[];
};
