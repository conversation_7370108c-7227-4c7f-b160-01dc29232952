import { isError } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import { Feedback } from '@cosmos/components/feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, observer } from '@globals/mobx';
import type { ObjectSelectorProps } from './types/object-selector.types';

/**
 * ObjectSelector modal container component that handles business logic
 * and delegates presentation to the view component.
 *
 * @param props - The component props.
 * @returns React JSX element.
 */
export const ObjectSelector = observer(
    <T,>({
        config,
        callbacks,
        controller,
        'data-id': dataId = 'object-selector',
        'data-testid': dataTestId = 'object-selector-modal',
    }: ObjectSelectorProps<T>): React.JSX.Element => {
        // ===== EVENT HANDLERS =====

        /**
         * Handle modal close action.
         * Calls appropriate callback and resets controller.
         */
        const handleClose = action(() => {
            try {
                // Call the callback first if provided
                if (callbacks.onCancel) {
                    callbacks.onCancel();
                }

                // Reset controller state
                controller.reset();

                // Close the modal using modalController
                modalController.closeModal(config.modal.id);
            } catch (error) {
                snackbarController.addSnackbar({
                    id: 'object-selector-close-error',
                    props: {
                        title: t`Error closing object selector`,
                        description: t`There was an error closing the object selector. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                logger.error({
                    message: isError(error)
                        ? error.message
                        : t`Error closing object selector modal`,
                });
            }
        });

        /**
         * Handle save action with appropriate callback execution.
         */
        const handleSave = action(() => {
            try {
                const { selectedItems } = controller;

                // Call the callback with selected items
                callbacks.onSelected(
                    config.selectionMode === 'multi'
                        ? selectedItems
                        : selectedItems[0],
                );

                // Close the modal after successful selection
                handleClose();
            } catch (error) {
                snackbarController.addSnackbar({
                    id: 'object-selector-save-error',
                    props: {
                        title: t`Error saving object selection`,
                        description: t`There was an error saving the object selection. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                logger.error({
                    message: isError(error)
                        ? error.message
                        : t`Error saving object selection`,
                });
            }
        });

        /**
         * Generate empty state component for search results.
         */
        const handleGetSearchEmptyState = () => {
            const objectType = config.type.toLowerCase();
            const message =
                config.search?.emptyStateMessage ||
                t`No ${objectType}s found matching your search criteria.`;

            return (
                <Text
                    type="body"
                    size="200"
                    colorScheme="neutral"
                    align="center"
                    data-id="empty-search-state"
                    data-testid="handleGetSearchEmptyState"
                >
                    {message}
                </Text>
            );
        };

        /**
         * Handle selection change from the combobox.
         * Converts ListBoxItemData back to ObjectItem and updates controller.
         */
        const handleOnChange = action(
            (selected: ListBoxItemData[] | ListBoxItemData) => {
                try {
                    // Convert ListBoxItemData back to ObjectItem and update controller
                    const selectedItems = Array.isArray(selected)
                        ? selected
                        : [selected];

                    // Remove duplicates by using a Map with id as key
                    const uniqueSelectedMap = new Map<
                        string,
                        ListBoxItemData
                    >();

                    for (const item of selectedItems) {
                        uniqueSelectedMap.set(item.id, item);
                    }

                    const uniqueSelected = [...uniqueSelectedMap.values()];

                    const objectItems: typeof controller.availableItems = [];

                    for (const selectedItem of uniqueSelected) {
                        const availableItem = controller.availableItems.find(
                            (objectItem) => objectItem.id === selectedItem.id,
                        );

                        if (availableItem) {
                            objectItems.push(availableItem);
                        } else {
                            const existingSelectedItem =
                                controller.selectedItems.find(
                                    (item) => item.id === selectedItem.id,
                                );

                            if (existingSelectedItem) {
                                objectItems.push(existingSelectedItem);
                            }
                        }
                    }

                    controller.setSelectedItems(objectItems);
                } catch (error) {
                    snackbarController.addSnackbar({
                        id: 'object-selector-selection-change-error',
                        props: {
                            title: t`Error updating object selection`,
                            description: t`There was an error updating the object selection. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    logger.error({
                        message: isError(error)
                            ? error.message
                            : t`Error updating object selection`,
                    });
                }
            },
        );

        /**
         * Handle fetch options for infinite scrolling and search.
         */
        const handleFetchOptions = action(
            (params: { search?: string; increasePage?: boolean }) => {
                try {
                    if (params.increasePage) {
                        controller.loadNextPage();
                    } else if (params.search !== undefined) {
                        controller.search(params.search);
                    }
                } catch (error) {
                    snackbarController.addSnackbar({
                        id: 'object-selector-fetch-options-error',
                        props: {
                            title: t`Error fetching object options`,
                            description: t`There was an error fetching the object options. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    logger.error({
                        message: isError(error)
                            ? error.message
                            : t`Error fetching object options`,
                        additionalInfo: {
                            error,
                            params,
                            objectType: config.type,
                        },
                    });
                }
            },
        );

        // ===== COMPUTED VALUES =====
        // All computed values are now handled by the controller

        /**
         * ===== HELPER FUNCTIONS =====.
         */
        const getErrorMessage = () => {
            const objectType = config.type.toLowerCase();

            return t`Error loading ${objectType}s`;
        };

        const getSearchLabel = () => {
            if (config.search?.label) {
                return config.search.label;
            }
            const objectType = config.type.toLowerCase();

            return t`Search ${objectType}s`;
        };

        const getSearchPlaceholder = () => {
            if (config.search?.placeholder) {
                return config.search.placeholder;
            }
            const objectType = config.type.toLowerCase();

            return t`Search by ${objectType} name...`;
        };

        // ===== RENDER =====

        // ===== ERROR STATE =====
        if (controller.hasError) {
            return (
                <>
                    <Modal.Header
                        title={config.modal.title}
                        closeButtonAriaLabel={t`Close object selector modal`}
                        data-id={`${dataId}-${config.type.toLowerCase()}-error-header`}
                        data-testid={
                            dataTestId
                                ? `${dataTestId}-error-header`
                                : 'object-selector-error-header'
                        }
                        onClose={handleClose}
                    />
                    <Modal.Body
                        data-id={`${dataId}-${config.type.toLowerCase()}-error-body`}
                        data-testid={
                            dataTestId
                                ? `${dataTestId}-error-body`
                                : 'object-selector-error-body'
                        }
                    >
                        <Feedback
                            title={getErrorMessage()}
                            severity="critical"
                            data-id={`${dataId}-error`}
                        />
                    </Modal.Body>
                    <Modal.Footer
                        data-id={`${dataId}-${config.type.toLowerCase()}-error-footer`}
                        data-testid={
                            dataTestId
                                ? `${dataTestId}-error-footer`
                                : 'object-selector-error-footer'
                        }
                        rightActionStack={[
                            {
                                label:
                                    config.modal.cancelButtonLabel || t`Close`,
                                level: 'secondary',
                                onClick: handleClose,
                            },
                        ]}
                    />
                </>
            );
        }

        /**
         * ===== MAIN RENDER =====.
         */
        return (
            <>
                <Modal.Header
                    title={controller.modalTitle}
                    closeButtonAriaLabel={t`Close object selector modal`}
                    data-id={`${dataId}-${config.type.toLowerCase()}-header`}
                    data-testid={
                        dataTestId
                            ? `${dataTestId}-header`
                            : 'object-selector-header'
                    }
                    description={
                        config.modal.showSelectedCount &&
                        config.selectionMode === 'multi'
                            ? controller.selectedCountText
                            : undefined
                    }
                    onClose={handleClose}
                />
                <Modal.Body
                    data-id={`${dataId}-${config.type.toLowerCase()}-body`}
                    data-testid={
                        dataTestId
                            ? `${dataTestId}-body`
                            : 'object-selector-body'
                    }
                >
                    <Stack direction="column" gap="lg">
                        <Box>
                            <ComboboxField
                                isMultiSelect={config.selectionMode === 'multi'}
                                formId={`${config.modal.id}-form`}
                                getSearchEmptyState={handleGetSearchEmptyState}
                                name="object-selector"
                                data-id={`${config.modal.id}-combobox`}
                                data-testid="object-selector-combobox"
                                options={controller.availableItems}
                                isLoading={controller.isLoading}
                                hasMore={controller.hasNextPage}
                                label={getSearchLabel()}
                                placeholder={getSearchPlaceholder()}
                                defaultSelectedOptions={controller.selectedItems.map(
                                    (item) => ({ ...item }),
                                )}
                                loaderLabel={
                                    config.search?.loaderLabel || t`Loading...`
                                }
                                removeAllSelectedItemsLabel={
                                    config.search?.clearAllLabel || t`Clear all`
                                }
                                getRemoveIndividualSelectedItemClickLabel={({
                                    itemLabel,
                                }) => t`Remove ${itemLabel}`}
                                onChange={handleOnChange}
                                onFetchOptions={handleFetchOptions}
                            />
                        </Box>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    data-id={`${dataId}-${config.type.toLowerCase()}-footer`}
                    data-testid={
                        dataTestId
                            ? `${dataTestId}-footer`
                            : 'object-selector-footer'
                    }
                    rightActionStack={[
                        {
                            label: config.modal.cancelButtonLabel || t`Cancel`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label:
                                config.modal.confirmButtonLabel || t`Confirm`,
                            level: 'primary',
                            onClick: handleSave,
                        },
                    ]}
                />
            </>
        );
    },
);
