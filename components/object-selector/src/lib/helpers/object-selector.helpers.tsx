import { modalController } from '@controllers/modal';
import { t } from '@globals/i18n/macro';
import { ObjectSelector } from '../object-selector.component';
import type {
    IObjectSelectorController,
    ObjectItem,
    ObjectSelectorCallbacks,
    ObjectSelectorConfig,
} from '../types/object-selector.types';

/**
 * Preset configurations for common use cases.
 */
export const getObjectSelectorPresets = (): Record<
    string,
    Partial<ObjectSelectorConfig>
> => ({
    /**
     * Policy selector for linking policies to controls.
     */
    linkPoliciesToControl: {
        selectionMode: 'multi' as const,
        modal: {
            id: 'link-policies-to-control',
            title: t`Link Policies to Control`,
            size: 'lg' as const,
            confirmButtonLabel: t`Link Policies`,
            cancelButtonLabel: t`Cancel`,
            showSelectedCount: true,
        },
        search: {
            placeholder: t`Search by policy name...`,
            label: t`Search policies`,
            loaderLabel: t`Loading...`,
            emptyStateMessage: t`No policies found matching your search criteria.`,
            clearAllLabel: t`Clear all`,
        },
    },

    /**
     * Control selector for mapping controls to policies.
     */
    mapControlsToPolicies: {
        selectionMode: 'multi' as const,
        modal: {
            id: 'map-controls-to-policies',
            title: t`Map Controls to Policies`,
            size: 'lg' as const,
            confirmButtonLabel: t`Map Controls`,
            cancelButtonLabel: t`Cancel`,
            showSelectedCount: true,
        },
        search: {
            placeholder: t`Search by control code or name...`,
            label: t`Search controls`,
            loaderLabel: t`Loading controls...`,
            emptyStateMessage: t`No controls found matching your search criteria.`,
            clearAllLabel: t`Clear all`,
        },
    },

    /**
     * Single policy selector for replacement scenarios.
     */
    replacePolicies: {
        selectionMode: 'multi' as const,
        modal: {
            id: 'replace-policies',
            title: t`Select Policies to Replace`,
            size: 'lg' as const,
            confirmButtonLabel: t`Replace`,
            cancelButtonLabel: t`Cancel`,
            showSelectedCount: true,
        },
        search: {
            placeholder: t`Search by policy name...`,
            label: t`Search policies`,
            loaderLabel: t`Loading policies...`,
            emptyStateMessage: t`No policies found matching your search criteria.`,
            clearAllLabel: t`Clear all`,
        },
    },

    /**
     * Single control selector for detailed views.
     */
    selectControlForDetails: {
        selectionMode: 'single' as const,
        modal: {
            id: 'select-control-for-details',
            title: t`Select Control`,
            size: 'md' as const,
            confirmButtonLabel: t`Select`,
            cancelButtonLabel: t`Cancel`,
            showSelectedCount: false,
        },
        search: {
            placeholder: t`Search by control code or name...`,
            label: t`Search controls`,
            loaderLabel: t`Loading...`,
            emptyStateMessage: t`No controls found matching your search criteria.`,
        },
    },
});

/**
 * Generic helper function to open an object selector modal.
 * This is useful when you need more control over the controller type.
 */
export const openObjectSelector = <T = unknown>(options: {
    config: ObjectSelectorConfig;
    callbacks: ObjectSelectorCallbacks<T>;
    controller: IObjectSelectorController<T>;
}): void => {
    const { config, callbacks, controller } = options;

    // Controller is required, no need for fallback
    const finalController = controller;

    // Initialize the controller before opening the modal
    // Pass default selected items if provided in config
    finalController.initialize(
        config,
        config.defaultSelectedItems as ObjectItem<T>[] | undefined,
    );

    modalController.openModal({
        id: config.modal.id,
        size: config.modal.size ?? 'lg',
        content: () => (
            <ObjectSelector
                config={config}
                callbacks={callbacks}
                controller={finalController}
                data-id={`object-selector-${config.type.toLowerCase()}-${config.modal.id}`}
                data-testid="object-selector-modal"
            />
        ),
    });
};
