import type { ListBoxItemData } from '@cosmos/components/list-box';

/**
 * Supported object types for the ObjectSelector.
 */
export type ObjectType = 'CONTROL' | 'POLICY' | 'EXTERNAL_POLICY_FILE';

/**
 * Selection mode for the object selector.
 */
export type ObjectSelectionMode = 'single' | 'multi';

/**
 * Generic object item that can represent different types of objects.
 */
export interface ObjectItem<T = unknown> extends ListBoxItemData {
    /**
     * The type of object this item represents.
     */
    objectType: ObjectType;

    /**
     * The raw data for this object.
     */
    objectData: T;

    /**
     * Additional metadata for the object.
     */
    metadata?: Record<string, unknown>;
}

/**
 * Configuration for the ObjectSelector modal.
 */
export interface ObjectSelectorConfig {
    /**
     * The type of objects to select.
     */
    type: ObjectType;

    /**
     * Selection mode (single or multi).
     */
    selectionMode: ObjectSelectionMode;

    /**
     * Modal configuration.
     */
    modal: {
        id: string;
        title: string;
        size?: 'sm' | 'md' | 'lg' | 'xl';
        confirmButtonLabel?: string;
        cancelButtonLabel?: string;
        showSelectedCount?: boolean;
        disableClickOutsideToClose?: boolean;
    };

    /**
     * Search configuration.
     */
    search?: {
        placeholder?: string;
        label?: string;
        loaderLabel?: string;
        emptyStateMessage?: string;
        clearAllLabel?: string;
    };

    /**
     * Filter options.
     */
    filters?: {
        excludeIds?: string[];
        includeInactive?: boolean;
        customFilters?: Record<string, unknown>;
    };

    /**
     * Default selected items to pre-populate the selector.
     * These items will be shown as selected when the modal opens.
     */
    defaultSelectedItems?: ObjectItem[];
}

/**
 * Callback functions for ObjectSelector events.
 */
export interface ObjectSelectorCallbacks<T = unknown> {
    /**
     * Called when the user confirms their selection.
     */
    onSelected: (selectedItems: ObjectItem<T>[] | ObjectItem<T>) => void;

    /**
     * Called when the user cancels the selection.
     */
    onCancel?: () => void;
}

/**
 * Props for the ObjectSelector container component.
 */
export interface ObjectSelectorProps<T = unknown> {
    /**
     * Configuration for the selector.
     */
    config: ObjectSelectorConfig;

    /**
     * Callback functions.
     */
    callbacks: ObjectSelectorCallbacks<T>;

    /**
     * Controller instance to use for data management.
     */
    controller: IObjectSelectorController<T>;

    /**
     * Data attributes for testing.
     */
    'data-id'?: string;
    'data-testid'?: string;
}

/**
 * Interface for ObjectSelector controllers.
 * This defines the contract that all object selector controllers must implement.
 */
export interface IObjectSelectorController<T = unknown> {
    // ===== OBSERVABLE STATE =====

    /**
     * Currently available items for selection.
     */
    readonly availableItems: ObjectItem<T>[];

    /**
     * Currently selected items.
     */
    readonly selectedItems: ObjectItem<T>[];

    /**
     * Current search term.
     */
    readonly searchTerm: string;

    /**
     * Loading state.
     */
    readonly isLoading: boolean;

    /**
     * Error state.
     */
    readonly hasError: boolean;

    /**
     * Error message if there's an error.
     */
    readonly errorMessage?: string;

    /**
     * Whether there are more items to load.
     */
    readonly hasNextPage: boolean;

    /**
     * Text showing the count of selected items.
     */
    readonly selectedCountText: string;

    /**
     * Modal title (may include selected count).
     */
    readonly modalTitle: string;

    /**
     * Whether the confirm button should be disabled.
     */
    readonly isConfirmDisabled: boolean;

    // ===== ACTIONS =====

    /**
     * Initialize the controller with configuration.
     * Optionally accepts default selected items to pre-populate the selection.
     */
    initialize: (
        config: ObjectSelectorConfig,
        defaultSelectedItems?: ObjectItem<T>[],
    ) => void;

    /**
     * Load items based on current configuration and filters.
     */
    loadItems: () => void;

    /**
     * Load the next page of items.
     */
    loadNextPage: () => void;

    /**
     * Search for items with the given term.
     */
    search: (searchTerm: string) => void;

    /**
     * Set the selected items.
     */
    setSelectedItems: (items: ObjectItem<T>[]) => void;

    /**
     * Add items to the current selection.
     */
    addSelectedItems: (items: ObjectItem<T>[]) => void;

    /**
     * Remove an item from the selection.
     */
    removeSelectedItem: (itemId: string) => void;

    /**
     * Clear all selected items.
     */
    clearSelectedItems: () => void;

    /**
     * Reset the controller state.
     */
    reset: () => void;
}
