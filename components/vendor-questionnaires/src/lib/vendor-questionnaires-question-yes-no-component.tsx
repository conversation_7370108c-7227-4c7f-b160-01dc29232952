import { noop } from 'lodash-es';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { RadioFieldGroup } from '@cosmos/components/radio-field-group';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import {
    createFollowUpTriggerHandler,
    createQuestionTextHandler,
    createQuestionToggleHandler,
} from './helpers/vendor-questionnaire-handlers.helper';
import type { VendorQuestionnairesQuestionYesNoProps } from './types/vendor-questionnaires.type';

export const VendorQuestionnairesQuestionYesNoComponent = ({
    includeFollowUpQn,
    followUpTrigger,
    followUpQn,
    isCreateMode = false,
    questionIndex,
}: VendorQuestionnairesQuestionYesNoProps): React.JSX.Element => {
    const followUpTriggerValue = followUpTrigger ? 'yes' : 'no';

    const handleIncludeFollowUpChange =
        questionIndex === undefined
            ? noop
            : createQuestionToggleHandler(
                  questionIndex,
                  'includeFollowUpQn',
                  includeFollowUpQn,
              );

    const handleFollowUpTriggerChange =
        questionIndex === undefined
            ? noop
            : createFollowUpTriggerHandler(questionIndex);

    const handleFollowUpQuestionChange =
        questionIndex === undefined
            ? noop
            : createQuestionTextHandler(questionIndex, 'followUpQn');

    return (
        <Grid
            gap={'6x'}
            data-testid="VendorQuestionnairesQuestionYesNoComponent"
            data-id="FkQ8OLqV"
        >
            <CheckboxField
                aria-labelledby={undefined}
                data-id={`${FORM_ID}-includeFollowup`}
                formId={FORM_ID}
                label={t`Include follow-up question`}
                name="followUp"
                value={String(includeFollowUpQn)}
                checked={includeFollowUpQn}
                onChange={isCreateMode ? handleIncludeFollowUpChange : noop}
            />
            {includeFollowUpQn && (
                <>
                    <RadioFieldGroup
                        data-id={`${FORM_ID}-includeFollowup`}
                        formId={FORM_ID}
                        label={t`Which answer do you want to trigger a follow-up?`}
                        name="followUpTrigger"
                        value={followUpTriggerValue}
                        options={[
                            {
                                value: 'yes',
                                label: t`Yes`,
                            },
                            {
                                value: 'no',
                                label: t`No`,
                            },
                        ]}
                        onChange={
                            isCreateMode ? handleFollowUpTriggerChange : noop
                        }
                    />

                    <TextField
                        data-id={`${FORM_ID}-followUpQuestion`}
                        formId={FORM_ID}
                        label={t`Follow-up question`}
                        name="followUpQuestion"
                        value={followUpQn}
                        onChange={
                            isCreateMode ? handleFollowUpQuestionChange : noop
                        }
                    />
                </>
            )}
        </Grid>
    );
};
