import { DragDropContext, type DropResult } from 'react-beautiful-dnd';
import {
    DraggableContainerComponent,
    DroppableContainerComponent,
} from '@components/droppable';
import {
    sharedVendorsQuestionnaireAddController,
    sharedVendorsTypeformQuestionnaireController,
} from '@controllers/vendors';
import { Accordion } from '@cosmos/components/accordion';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import { VendorQuestionnairesQuestionItemComponent } from './vendor-questionnaires-question-item-component';

interface VendorQuestionnairesQuestionAccordionComponentProps {
    isCreateMode?: boolean;
}

export const VendorQuestionnairesQuestionAccordionComponent = observer(
    ({
        isCreateMode = false,
    }: VendorQuestionnairesQuestionAccordionComponentProps = {}): React.JSX.Element => {
        const readController = sharedVendorsTypeformQuestionnaireController;
        const createController = sharedVendorsQuestionnaireAddController;

        const fields = isCreateMode
            ? createController.formModel.formData.questions.map((q, index) => ({
                  ...q,
                  ref: q.id || `question-${index}`,
              }))
            : (readController.vendorsQuestionnaireQuery.data?.fields ?? []);

        const handleDragEnd = (result: DropResult) => {
            if (!result.destination || !isCreateMode) {
                return;
            }

            const sourceIndex = result.source.index;
            const destinationIndex = result.destination.index;

            if (sourceIndex === destinationIndex) {
                return;
            }

            // Check if it's a choice drag (draggableId starts with "choice-")
            if (result.draggableId.startsWith('choice-')) {
                // Extract question index from droppableId (format: "choices-question-X")
                const questionIndexMatch = result.source.droppableId.match(
                    /choices-question-(\d+)/,
                );

                if (questionIndexMatch) {
                    const questionIndex = parseInt(questionIndexMatch[1], 10);
                    const currentChoices = [
                        ...(createController.formModel.formData.questions[
                            questionIndex
                        ].choices ?? []),
                    ];
                    const [removed] = currentChoices.splice(sourceIndex, 1);

                    currentChoices.splice(destinationIndex, 0, removed);
                    createController.formModel.updateQuestion(
                        questionIndex,
                        'choices',
                        currentChoices,
                    );
                }
            } else {
                // It's a question drag
                createController.formModel.reorderQuestions(
                    sourceIndex,
                    destinationIndex,
                );
            }
        };

        const handleAddQuestion = () => {
            if (isCreateMode) {
                createController.formModel.addQuestion();
            }
        };

        return (
            <Grid
                gap="6x"
                data-testid="VendorQuestionnairesQuestionAccordionComponent"
                data-id="tI-CH-Bv"
            >
                <DragDropContext onDragEnd={handleDragEnd}>
                    <DroppableContainerComponent
                        droppableId="questionary-dnd"
                        type="field"
                    >
                        {fields.map((item, index) => (
                            <DraggableContainerComponent
                                key={item.ref}
                                draggableId={item.ref}
                                index={index}
                                data-id="lioRr_Cx"
                            >
                                {() => (
                                    <Stack
                                        gap="2x"
                                        p="1x"
                                        direction="column"
                                        data-id="2d9i02t7"
                                    >
                                        <Accordion
                                            title={`${index + 1}. ${item.title}`}
                                            data-id={`${FORM_ID}-${index + 1}-question`}
                                            iconSlot={{
                                                slotType: 'icon',
                                                typeProps: {
                                                    name: 'ReorderDotsVertical',
                                                    colorScheme: 'neutral',
                                                },
                                            }}
                                            body={
                                                <VendorQuestionnairesQuestionItemComponent
                                                    item={item}
                                                    isCreateMode={isCreateMode}
                                                    questionIndex={index}
                                                />
                                            }
                                        />
                                        <Stack align="center" justify="center">
                                            <Button
                                                isIconOnly
                                                label={t`Add question`}
                                                level="tertiary"
                                                startIconName="Plus"
                                                colorScheme="primary"
                                                onClick={handleAddQuestion}
                                            />
                                        </Stack>
                                    </Stack>
                                )}
                            </DraggableContainerComponent>
                        ))}
                    </DroppableContainerComponent>
                </DragDropContext>
            </Grid>
        );
    },
);
