import { noop } from 'lodash-es';
import {
    sharedVendorsQuestionnaireAddController,
    sharedVendorsTypeformQuestionnaireController,
} from '@controllers/vendors';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import { createMarkAllAsRequiredHandler } from './helpers/vendor-questionnaire-handlers.helper';
import { VendorQuestionnairesQuestionAccordionComponent } from './vendor-questionnaires-question-accordion-component';

interface VendorQuestionnairesQuestionsComponentProps {
    isCreateMode?: boolean;
}

export const VendorQuestionnairesQuestionsComponent = observer(
    ({
        isCreateMode = false,
    }: VendorQuestionnairesQuestionsComponentProps = {}): React.JSX.Element => {
        const readController = sharedVendorsTypeformQuestionnaireController;
        const createController = sharedVendorsQuestionnaireAddController;

        const areAllAsRequired = isCreateMode
            ? createController.formModel.formData.markAllAsRequired
            : readController.areAllAsRequired;

        const handleMarkAllAsRequiredChange = createMarkAllAsRequiredHandler();

        return (
            <Grid
                gap={'6x'}
                data-testid="VendorQuestionnairesQuestionsComponent"
                data-id="4Sk7zw68"
            >
                <CheckboxField
                    aria-labelledby={undefined}
                    data-id={`${FORM_ID}-areAllAsRequired`}
                    formId={FORM_ID}
                    label={t`Mark all questions as required`}
                    name="areAllAsRequired"
                    value={String(areAllAsRequired)}
                    checked={areAllAsRequired}
                    onChange={
                        isCreateMode ? handleMarkAllAsRequiredChange : noop
                    }
                />
                <VendorQuestionnairesQuestionAccordionComponent
                    isCreateMode={isCreateMode}
                />
            </Grid>
        );
    },
);
