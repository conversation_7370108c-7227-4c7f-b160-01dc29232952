import { isNumber, noop } from 'lodash-es';
import { But<PERSON> } from '@cosmos/components/button';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import {
    createChoiceAddHandler,
    createChoiceDeleteHandler,
    createChoiceUpdateHandler,
    createQuestionToggleHandler,
} from './helpers/vendor-questionnaire-handlers.helper';
import type { VendorQuestionnairesQuestionChoicesProps } from './types/vendor-questionnaires.type';
import { VendorQuestionnairesQuestionChoiceListComponent } from './vendor-questionnaires-question-choice-list-component';

export const VendorQuestionnairesQuestionChoicesComponent = ({
    choices,
    allowOtherChoice,
    isCreateMode = false,
    questionIndex,
}: VendorQuestionnairesQuestionChoicesProps): React.JSX.Element => {
    const handleAllowOtherChoiceChange = isNumber(questionIndex)
        ? createQuestionToggleHandler(
              questionIndex,
              'allowOtherChoice',
              Boolean(allowOtherChoice),
          )
        : noop;

    const handleAddChoice = isNumber(questionIndex)
        ? createChoiceAddHandler(questionIndex)
        : noop;

    const handleDeleteChoice = isNumber(questionIndex)
        ? createChoiceDeleteHandler(questionIndex)
        : noop;

    const handleChoiceUpdate = isNumber(questionIndex)
        ? createChoiceUpdateHandler(questionIndex)
        : noop;

    return (
        <Grid
            gap="6x"
            data-testid="VendorQuestionnairesQuestionChoicesComponent"
            data-id="0biNtLcY"
        >
            <CheckboxField
                aria-labelledby={undefined}
                data-id={`${FORM_ID}-addCustomResponse`}
                formId={FORM_ID}
                label={t`Let people add a custom response`}
                name="addCustomResponse"
                value={String(allowOtherChoice)}
                checked={allowOtherChoice}
                onChange={isCreateMode ? handleAllowOtherChoiceChange : noop}
            />

            <VendorQuestionnairesQuestionChoiceListComponent
                choices={choices}
                isCreateMode={isCreateMode}
                questionIndex={questionIndex}
                onDelete={isCreateMode ? handleDeleteChoice : noop}
                onChoiceUpdate={isCreateMode ? handleChoiceUpdate : noop}
            />

            <Stack align="center" justify="start">
                <Button
                    data-id={`${FORM_ID}-addOption`}
                    label={t`Add option`}
                    startIconName="Plus"
                    level="tertiary"
                    onClick={isCreateMode ? handleAddChoice : noop}
                />
            </Stack>
        </Grid>
    );
};
