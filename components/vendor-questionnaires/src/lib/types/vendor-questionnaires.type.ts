import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    QuestionnaireFieldResponseDto,
    QuestionnaireVendorResponseDto,
} from '@globals/api-sdk/types';

// Base response types
export type VendorQuestionnaireResponseType =
    QuestionnaireVendorResponseDto['fields'][number]['type'][number];

export type VendorQuestionnaireShortAnswerType =
    | 'TEXT'
    | 'EMAIL'
    | 'URL'
    | 'PHONE';

export type VendorQuestionnaireCategoryType =
    QuestionnaireVendorResponseDto['categories'][number];

export type VendorQuestionnaireRiskType =
    QuestionnaireVendorResponseDto['riskLevels'][number];

export type VendorQuestionnaireStatus =
    QuestionnaireVendorResponseDto['status'];

export type VendorQuestionnaireField =
    QuestionnaireVendorResponseDto['fields'][number] & {
        ref: string;
    };

export interface VendorQuestionnaireFieldItem {
    ref: string;
    id?: string;
    title: string;
    type: string;
    required: boolean;
    shortAnswerType?: string;
    choices?: { ref: string; label: string }[];
    followUpQn?: string;
    allowOtherChoice?: boolean;
    includeFollowUpQn?: boolean;
    followUpQnTrigger?: boolean;
}

// Type for question property keys that can be updated
export type QuestionPropertyKey =
    | 'title'
    | 'id'
    | 'type'
    | 'required'
    | 'shortAnswerType'
    | 'choices'
    | 'followUpQn'
    | 'allowOtherChoice'
    | 'includeFollowUpQn'
    | 'followUpQnTrigger';

export type VendorQuestionnaireRowData = QuestionnaireVendorResponseDto;

// Field and Choice types
export interface VendorQuestionnaireFieldChoice {
    ref: string;
    label: string;
}

// Link type
export interface VendorQuestionnaireExternalLink {
    id: number;
    url: string;
    typeFormId: string;
}

export interface VendorQuestionnaireRow {
    row: {
        original: VendorQuestionnaireRowData;
    };
}

// Component Props types
export interface VendorQuestionItemDetailsProps {
    type: VendorQuestionnaireResponseType;
    allowOtherChoice: boolean;
    includeFollowUpQn: boolean;
    choices: VendorQuestionnaireFieldChoice[];
    followUpQn: string;
    followUpTrigger: boolean;
}

export interface VendorQuestionnairesQuestionYesNoProps {
    includeFollowUpQn: boolean;
    followUpTrigger: boolean;
    followUpQn: string;
    isCreateMode?: boolean;
    questionIndex?: number;
}

export interface VendorQuestionnairesQuestionChoicesProps {
    choices: VendorQuestionnaireFieldChoice[];
    allowOtherChoice: boolean;
    isCreateMode?: boolean;
    questionIndex?: number;
}

export interface VendorQuestionnairesQuestionItemProps {
    item: QuestionnaireFieldResponseDto | VendorQuestionnaireFieldItem;
    isCreateMode?: boolean;
    questionIndex?: number;
}

export interface VendorQuestionnairesQuestionResponseTypeProps {
    item: QuestionnaireFieldResponseDto | VendorQuestionnaireFieldItem;
    isCreateMode?: boolean;
    questionIndex?: number;
}

export interface VendorQuestionResponseTypeProps {
    onResponseTypeChange: (item: ListBoxItemData) => void;
}

export interface VendorQuestionChoiceListProps {
    choices: VendorQuestionnaireFieldChoice[];
    onDelete: (choice: VendorQuestionnaireFieldChoice) => void;
}

// Cell Props types
export type VendorQuestionnaireCellStatusProps = VendorQuestionnaireRow;
export type VendorQuestionnaireCellCategoriesProps = VendorQuestionnaireRow;
export type VendorQuestionnaireCellRiskLevelProps = VendorQuestionnaireRow;
