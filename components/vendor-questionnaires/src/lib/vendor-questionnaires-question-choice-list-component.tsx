import { noop } from 'lodash-es';
import {
    DraggableContainerComponent,
    DroppableContainerComponent,
} from '@components/droppable';
import { Button } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import type { VendorQuestionChoiceListProps } from './types/vendor-questionnaires.type';

export const VendorQuestionnairesQuestionChoiceListComponent = observer(
    ({
        choices,
        onDelete,
        isCreateMode = false,
        onChoiceUpdate,
        questionIndex,
    }: VendorQuestionChoiceListProps & {
        isCreateMode?: boolean;
        onChoiceUpdate?: (choiceIndex: number, newLabel: string) => void;
        questionIndex?: number;
    }): React.JSX.Element => {
        const droppableId = `choices-question-${questionIndex ?? 'default'}`;

        const plainChoices = toJS(choices);

        return (
            <DroppableContainerComponent
                droppableId={droppableId}
                type="choice"
                data-testid="VendorQuestionnairesQuestionChoiceListComponent"
                data-id="ae8hSEIV"
            >
                {plainChoices.map((item, index) => (
                    <DraggableContainerComponent
                        key={item.ref}
                        draggableId={`choice-${questionIndex}-${item.ref}`}
                        index={index}
                        data-id="SAmTCs5J"
                    >
                        {() => (
                            <Stack
                                align="center"
                                gap="2x"
                                py="2x"
                                justify="center"
                                direction="row"
                                data-id="jlekCzS0"
                            >
                                <Icon name="ReorderDotsVertical" />
                                <TextField
                                    shouldHideLabel
                                    data-id={`${FORM_ID}-option-value`}
                                    formId={FORM_ID}
                                    label={t`Option`}
                                    name="optionValue"
                                    value={item.label}
                                    onChange={
                                        isCreateMode && onChoiceUpdate
                                            ? (event) => {
                                                  onChoiceUpdate(
                                                      index,
                                                      event.target.value,
                                                  );
                                              }
                                            : noop
                                    }
                                />
                                <Button
                                    isIconOnly
                                    label={t`Delete choice`}
                                    level="tertiary"
                                    startIconName="Close"
                                    onClick={() => {
                                        onDelete(item);
                                    }}
                                />
                            </Stack>
                        )}
                    </DraggableContainerComponent>
                ))}
            </DroppableContainerComponent>
        );
    },
);
