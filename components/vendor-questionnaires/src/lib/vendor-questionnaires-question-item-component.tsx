import { isNil, isNumber, noop } from 'lodash-es';
import { Button } from '@cosmos/components/button';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import {
    createQuestionCopyHandler,
    createQuestionRemoveHandler,
    createQuestionTextHandler,
    createQuestionToggleHandler,
} from './helpers/vendor-questionnaire-handlers.helper';
import type { VendorQuestionnairesQuestionItemProps } from './types/vendor-questionnaires.type';
import { VendorQuestionnairesQuestionItemDetailsComponent } from './vendor-questionnaires-question-item-details-component';
import { VendorQuestionnairesQuestionResponseTypeComponent } from './vendor-questionnaires-question-response-type-component';

export const VendorQuestionnairesQuestionItemComponent = ({
    item,
    isCreateMode = false,
    questionIndex,
}: VendorQuestionnairesQuestionItemProps): React.JSX.Element => {
    // Extract properties with safe defaults
    const title = String(item.title || '');
    const { type } = item;
    const required = Boolean(item.required);
    const allowOtherChoice = Boolean(item.allowOtherChoice);
    const choices = Array.isArray(item.choices) ? item.choices : [];
    const includeFollowUpQn = Boolean(item.includeFollowUpQn);
    const followUpQn = String(item.followUpQn || '');
    const followUpQnTrigger = Boolean(item.followUpQnTrigger);

    const handleTitleChange = isNumber(questionIndex)
        ? createQuestionTextHandler(questionIndex, 'title')
        : noop;

    const handleRequiredChange = isNumber(questionIndex)
        ? createQuestionToggleHandler(questionIndex, 'required', required)
        : noop;

    const handleRemoveQuestion = isNumber(questionIndex)
        ? createQuestionRemoveHandler(questionIndex)
        : noop;

    const handleCopyQuestion = isNumber(questionIndex)
        ? createQuestionCopyHandler(questionIndex, item)
        : noop;

    return (
        <Grid
            gap="6x"
            data-testid="VendorQuestionnairesQuestionItemComponent"
            data-id="nMpzR8N4"
        >
            <TextField
                data-id={`${FORM_ID}-questionName`}
                formId={FORM_ID}
                label={t`Question`}
                name="questionName"
                value={title}
                onChange={isCreateMode ? handleTitleChange : noop}
            />
            <VendorQuestionnairesQuestionResponseTypeComponent
                item={item}
                isCreateMode={isCreateMode}
                questionIndex={questionIndex}
            />
            {!isNil(type) && (
                <VendorQuestionnairesQuestionItemDetailsComponent
                    type={String(type)}
                    allowOtherChoice={allowOtherChoice}
                    includeFollowUpQn={includeFollowUpQn}
                    followUpQn={followUpQn}
                    followUpTrigger={followUpQnTrigger}
                    isCreateMode={isCreateMode}
                    questionIndex={questionIndex}
                    choices={choices}
                />
            )}
            <CheckboxField
                aria-labelledby={undefined}
                data-id={`${FORM_ID}-markQuestionAsRequired`}
                formId={FORM_ID}
                label={t`Mark question as required`}
                name="markQuestionAsRequired"
                value={String(required)}
                checked={required}
                onChange={isCreateMode ? handleRequiredChange : noop}
            />
            <Stack direction="row" justify="end">
                <Button
                    isIconOnly
                    label={t`Copy`}
                    colorScheme="primary"
                    level="tertiary"
                    startIconName="Copy"
                    onClick={isCreateMode ? handleCopyQuestion : noop}
                />
                <Button
                    isIconOnly
                    label={t`Delete`}
                    colorScheme="danger"
                    level="tertiary"
                    startIconName="Trash"
                    onClick={isCreateMode ? handleRemoveQuestion : noop}
                />
            </Stack>
        </Grid>
    );
};
