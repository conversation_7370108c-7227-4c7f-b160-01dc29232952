import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { monitorsControllerListControlTestInstancesOptions } from '@globals/api-sdk/queries';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitorsController {
    constructor() {
        makeAutoObservable(this);
    }

    pagination = {
        page: 1,
        pageSize: 10,
    };

    filters = {
        search: '',
        sortByName: false,
        controlId: null as number | null,
    };

    controlId = 0;

    monitorsQuery = new ObservedQuery(
        monitorsControllerListControlTestInstancesOptions,
    );

    invalidate = (): void => {
        this.monitorsQuery.invalidate();
    };

    setControlId(controlId: number | null): void {
        if (!controlId) {
            throw new Error('Control ID is required');
        }

        this.filters.controlId = controlId;
        this.controlId = controlId;
    }

    loadMonitors = (
        controlIdParam?: number,
        params?: FetchDataResponseParams,
    ): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                if (params) {
                    this.pagination = {
                        page: params.pagination.page || 1,
                        pageSize: params.pagination.pageSize || 10,
                    };

                    this.filters.search = params.globalFilter.search || '';
                    this.filters.sortByName =
                        params.sorting[0]?.id === 'name' &&
                        params.sorting[0]?.desc;
                }

                type Query = Required<
                    Parameters<
                        typeof monitorsControllerListControlTestInstancesOptions
                    >
                >[0]['query'];

                const queryParams: Query = {
                    page: this.pagination.page,
                    limit: this.pagination.pageSize,
                    q: this.filters.search,
                    sortByName: this.filters.sortByName,
                    includeDrafts: true,
                };

                if (this.filters.controlId !== null) {
                    queryParams.controlId = controlIdParam ?? this.controlId;
                }

                this.monitorsQuery.load({
                    path: {
                        xProductId: sharedWorkspacesController.currentWorkspace
                            ?.id as number,
                    },
                    query: queryParams,
                });
            },
        );
    };

    get monitors(): ControlTestResponseDto[] {
        return this.monitorsQuery.data?.data ?? [];
    }

    get monitorsTotal(): number {
        return this.monitorsQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.monitorsQuery.isLoading;
    }

    get hasFilters(): boolean {
        return !isEmpty(this.filters.search);
    }
}

export const sharedMonitorsController = new MonitorsController();
