import { isEmpty, isObject } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { FilterStateValue } from '@cosmos/components/filter-field';
import {
    grcControllerGetAllRequirementsOptions,
    grcControllerUpdateRequirementsScopeMutation,
} from '@globals/api-sdk/queries';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedRequirementDetailsController } from './requirement-details-controller';
import type { UpdateRequirementsScopeProps } from './types/requirement-props.type';

// Define a type for mapped requirements that includes framework information
export interface MappedRequirement extends RequirementListResponseDto {
    frameworkId: number;
}

type Query = NonNullable<
    Parameters<typeof grcControllerGetAllRequirementsOptions>[0]['query']
>;

class RequirementsController {
    frameworkId: number;

    mappedRequirements: MappedRequirement[] = [];

    category: FilterStateValue;
    subcategory: FilterStateValue;
    level: FilterStateValue;
    isReady: FilterStateValue;
    isInScope: FilterStateValue;
    isParameterCompleted: FilterStateValue;
    topic: FilterStateValue;
    hasEverHadData = false;

    constructor() {
        this.frameworkId = 0;

        makeAutoObservable(this);
    }

    getAllRequirementsQuery = new ObservedQuery(
        grcControllerGetAllRequirementsOptions,
    );

    updateRequirementsScopeMutation = new ObservedMutation(
        grcControllerUpdateRequirementsScopeMutation,
    );

    set frameworkDetailsId(id: number) {
        this.frameworkId = id;
    }

    get requirements(): RequirementListResponseDto[] {
        return this.getAllRequirementsQuery.data?.data ?? [];
    }

    get requirementsTotal(): number {
        return this.getAllRequirementsQuery.data?.total ?? 0;
    }

    /**
     * This is used in a table load, it need the extra state.
     */
    get requirementsIsLoading(): boolean {
        return (
            this.getAllRequirementsQuery.isLoading ||
            this.getAllRequirementsQuery.isReady ||
            this.getAllRequirementsQuery.isFetching
        );
    }

    /**
     * # ⚠️ Warning
     * - Do not use in a table load, that requires extra state.
     * - Instead use `this.requirementsIsLoading`.
     */
    get areRequirementsLoading(): boolean {
        return this.getAllRequirementsQuery.isLoading;
    }

    get isUpdatingScope(): boolean {
        return this.updateRequirementsScopeMutation.isPending;
    }

    get shouldShowFirstTimeEmpty(): boolean {
        return !this.hasEverHadData && this.requirementsTotal === 0;
    }

    clearMappedRequirements(): void {
        this.mappedRequirements = [];
    }

    markDataLoaded(): void {
        // Don't update flag if:
        // 1. Already marked as having seen data, OR
        // 2. Current API response has zero requirements
        if (this.hasEverHadData || this.requirementsTotal === 0) {
            return;
        }

        // - hasEverHadData = false → "No data" empty state (first time)
        // - hasEverHadData = true → "No results" empty state (filtered/searched)
        this.hasEverHadData = true;
    }

    loadRequirements = (params: FetchDataResponseParams): void => {
        const { pagination, globalFilter } = params;
        const { page, pageSize } = pagination;
        const { search, filters } = globalFilter;

        const query: Query = {
            page,
            q: search ?? '',
            limit: pageSize,
            frameworkId: this.frameworkId,
        };

        // Wait for workspace to be available before making the API call
        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                const {
                    isReady = { value: undefined },
                    category = { value: undefined },
                    isInScope = { value: undefined },
                    level = { value: undefined },
                    isParameterCompleted = { value: undefined },
                    topic = { value: undefined },
                } = filters;

                // Find subcategory filter dynamically (ID format: subcategory-{categoryValue})
                const subcategoryFilter = Object.entries(filters).find(
                    ([key]) => key.startsWith('subcategory-'),
                );
                const subcategory = subcategoryFilter?.[1];

                this.isReady = isReady.value;

                this.category =
                    category.value &&
                    isObject(category.value) &&
                    'value' in category.value
                        ? (category.value as { value: string }).value
                        : category.value;

                if (subcategory) {
                    this.subcategory = subcategory.value;
                } else {
                    this.subcategory = undefined;
                }

                this.level = level.value;
                this.isInScope = isInScope.value;
                this.isParameterCompleted = isParameterCompleted.value;
                this.topic = topic.value;

                if (this.isReady) {
                    query.isReady = this.isReady === 'true';
                }

                if (this.category) {
                    query.category = this.category as typeof query.category;
                }

                if (this.subcategory) {
                    query.subCategory = this
                        .subcategory as typeof query.subCategory;
                }

                if (this.level) {
                    query.level = this.level as typeof query.level;
                }

                if (this.isInScope !== undefined) {
                    query.isInScope = this.isInScope === 'true';
                }

                if (this.isParameterCompleted !== undefined) {
                    query.isParameterCompleted =
                        this.isParameterCompleted === 'true';
                }

                if (this.topic) {
                    query.topic = topic.value as typeof query.topic;
                }

                this.getAllRequirementsQuery.load({
                    path: { xProductId: Number(currentWorkspace.id) },
                    query,
                });
            },
        );
    };

    updateRequirementsScope = ({
        requirementIds,
        isInScope,
        rationale,
        onSuccess,
    }: UpdateRequirementsScopeProps): void => {
        if (
            isEmpty(requirementIds) ||
            this.updateRequirementsScopeMutation.isPending
        ) {
            return;
        }

        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        this.updateRequirementsScopeMutation.mutate({
            path: { xProductId: workspaceId },
            body: {
                requirementIds,
                isInScope,
                rationale,
            },
        });

        when(
            () => !this.updateRequirementsScopeMutation.isPending,
            () => {
                if (this.updateRequirementsScopeMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'update-requirements-scope-error',
                        props: {
                            title: t`Failed to update requirement scope`,
                            description: t`An error occurred while updating the requirement scope. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    const scopeText = isInScope ? t`in scope` : t`out of scope`;

                    if (onSuccess) {
                        onSuccess();
                    }
                    snackbarController.addSnackbar({
                        id: 'update-requirements-scope-success',
                        props: {
                            title: t`Requirement marked ${scopeText} successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    // Refresh the requirements list
                    this.getAllRequirementsQuery.invalidate();
                    sharedRequirementDetailsController.requirementDetailsQuery.invalidate();
                }
            },
        );
    };
}

export const sharedRequirementsController = new RequirementsController();

// Auto-update data flag when requirements count changes (for empty state logic)
reaction(
    () => sharedRequirementsController.requirementsTotal,
    () => {
        sharedRequirementsController.markDataLoaded();
    },
);
