import { isNil, isString } from 'lodash-es';
import { sharedCustomFrameworkRequirementsController } from '@controllers/frameworks';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    customFrameworksControllerGetCustomCategoriesOptions,
    customFrameworksControllerSaveCustomRequirementMutation,
    customFrameworksControllerValidateCustomRequirementCodeOptions,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';

class RequirementCreateController {
    #getRequirementCategories = new ObservedQuery(
        customFrameworksControllerGetCustomCategoriesOptions,
    );

    #saveRequirementMutation = new ObservedMutation(
        customFrameworksControllerSaveCustomRequirementMutation,
    );

    #validateRequirementCode = new ObservedQuery(
        customFrameworksControllerValidateCustomRequirementCodeOptions,
    );

    #frameworkId: number | null = null;

    #isCreateComplete = false;

    constructor() {
        makeAutoObservable(this);
    }

    setFrameworkId(frameworkId: number): void {
        this.#frameworkId = frameworkId;
    }

    _requirementCategoriesOptions: ListBoxItemData[] = [];

    get requirementCategoriesOptions(): ListBoxItemData[] {
        return this._requirementCategoriesOptions;
    }

    loadRequirementCategories(frameworkId: number): void {
        this.#getRequirementCategories.load({
            path: { frameworkId },
            query: {
                page: 1,
            },
        });

        when(
            () => !this.#getRequirementCategories.isLoading,
            () => {
                if (this.#getRequirementCategories.hasError) {
                    console.error(
                        'Full error object:',
                        this.#getRequirementCategories.data,
                    );

                    snackbarController.addSnackbar({
                        id: 'requirement-categories-load-error',
                        props: {
                            title: t`Failed to load requirement categories`,
                            description: t`An error occurred while loading the requirement categories. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    const data =
                        this.#getRequirementCategories.data?.data ?? [];

                    this._requirementCategoriesOptions = data.map(
                        (category, index) => ({
                            id: `${category.label}-${index}`,
                            label: category.label,
                            value: category.label,
                        }),
                    );

                    this.#getRequirementCategories.invalidate();
                }
            },
        );
    }

    _selectedCategory: string | null = null;

    get selectedCategoryOption(): ListBoxItemData | undefined {
        return this.requirementCategoriesOptions.find(
            (option) => option.value === this._selectedCategory,
        );
    }

    setSelectedCategory(category: string | null): void {
        this._selectedCategory = category;

        if (
            !isNil(this._selectedCategory) &&
            !isNil(this._requirementCategoriesOptions) &&
            !this._requirementCategoriesOptions.some(
                (option) => option.value === this._selectedCategory,
            )
        ) {
            this._requirementCategoriesOptions.push({
                id: `category-${this._selectedCategory}`,
                label: this._selectedCategory,
                value: this._selectedCategory,
            });
        }
    }

    get isRequirementCategoriesLoading(): boolean {
        return this.#getRequirementCategories.isLoading;
    }

    get isCreateComplete(): boolean {
        return this.#isCreateComplete;
    }

    addCategory(categoryName: string): void {
        const newCategory = {
            id: `category-${categoryName}`,
            label: categoryName,
            value: categoryName,
        };

        const existingCategory = this._requirementCategoriesOptions.find(
            (option) => {
                if (!isString(option.value) || !isString(categoryName)) {
                    return false;
                }

                return (
                    option.value
                        .toLowerCase()
                        .trim()
                        .localeCompare(categoryName.toLowerCase().trim()) === 0
                );
            },
        );

        if (existingCategory && existingCategory.value) {
            this._selectedCategory = existingCategory.value;

            return;
        }

        this._requirementCategoriesOptions = [
            ...this.requirementCategoriesOptions,
            newCategory,
        ];

        this._selectedCategory = categoryName;
    }

    get isSavingRequirement(): boolean {
        return (
            this.#saveRequirementMutation.isPending ||
            this.#validateRequirementCode.isLoading
        );
    }

    saveRequirement = (
        formValues: FormValues,
        onSuccess?: () => void,
    ): void => {
        this.#isCreateComplete = false;

        if (isNil(this.#frameworkId)) {
            console.error('Framework ID is not set');

            return;
        }

        if (isNil(sharedWorkspacesController.currentWorkspace)) {
            console.error('Workspace ID is not set');

            return;
        }

        this.#validateRequirementCode.load({
            path: { customFrameworkId: this.#frameworkId },
            query: { requirementCode: formValues.requirementCode as string },
        });

        when(
            () => !this.#validateRequirementCode.isLoading,
            () => {
                if (this.#validateRequirementCode.hasError) {
                    console.error(
                        'Full error object:',
                        this.#validateRequirementCode.data,
                    );

                    snackbarController.addSnackbar({
                        id: 'requirement-save-error',
                        props: {
                            title: t`Unable to save requirement`,
                            description: t`An error occurred while saving the requirement. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    if (isNil(this.#frameworkId)) {
                        console.error('Framework ID is not set');
                        snackbarController.addSnackbar({
                            id: 'requirement-save-error',
                            props: {
                                title: t`Unable to save requirement`,
                                description: t`An error occurred while saving the requirement. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    const requirement =
                        this.#validateRequirementCode.data?.requirement;

                    if (
                        !isNil(requirement) &&
                        isString(requirement.code) &&
                        isString(formValues.requirementCode) &&
                        requirement.code.toLocaleLowerCase().trim() ===
                            formValues.requirementCode
                                .toLocaleLowerCase()
                                .trim()
                    ) {
                        snackbarController.addSnackbar({
                            id: 'requirement-code-validation-error',
                            props: {
                                title: t`Unable to save requirement`,
                                description: t`The requirement code is already in use. Please choose another code.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    let requirementCategory = formValues.requirementCategory as
                        | ListBoxItemData
                        | undefined;

                    requirementCategory ??= this.selectedCategoryOption;

                    this.#saveRequirementMutation.mutate({
                        body: {
                            code: (formValues.requirementCode ?? '') as string,
                            name: (formValues.requirementName ?? '') as string,
                            category: requirementCategory?.value,
                            description: formValues.description as string,
                            additionalInfo: formValues.additionalInfo as string,
                            frameworkId: this.#frameworkId,
                            workspaceId:
                                sharedWorkspacesController.currentWorkspace?.id,
                        },
                    });

                    when(
                        () => !this.#saveRequirementMutation.isPending,
                        () => {
                            if (this.#saveRequirementMutation.hasError) {
                                console.error(
                                    'Full error object:',
                                    this.#saveRequirementMutation.error,
                                );

                                snackbarController.addSnackbar({
                                    id: 'requirement-save-error',
                                    props: {
                                        title: t`Unable to save requirement`,
                                        description: t`An error occurred while saving the requirement. Please try again.`,
                                        severity: 'critical',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });
                            } else {
                                snackbarController.addSnackbar({
                                    id: 'requirement-save-success',
                                    props: {
                                        title: t`Requirement saved`,
                                        description: t`The requirement was saved successfully.`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                this.#isCreateComplete = true;

                                this.#getRequirementCategories.invalidate();
                                sharedCustomFrameworkRequirementsController.customFrameworkRequirementsQuery.invalidate();

                                onSuccess?.();
                            }
                        },
                    );
                }
            },
        );
    };

    reset = (): void => {
        this.#isCreateComplete = false;
        this._selectedCategory = null;
        this.#getRequirementCategories.invalidate();
    };
}

export const sharedRequirementCreateController =
    new RequirementCreateController();
