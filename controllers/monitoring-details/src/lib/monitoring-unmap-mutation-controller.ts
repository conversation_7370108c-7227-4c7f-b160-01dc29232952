import { sharedControlsDetailsStatsController } from '@controllers/controls';
import { sharedMonitorsController } from '@controllers/monitors';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerBulkDeleteControlTestsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringControlTestComparisonController } from './monitoring-control-test-comparsion-controller';

class MonitingUnmapMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    unmapMutation = new ObservedMutation(
        grcControllerBulkDeleteControlTestsMutation,
        {
            onSuccess: () => {
                sharedControlsDetailsStatsController.invalidate();
                sharedMonitoringControlTestComparisonController.invalidate();
                sharedMonitorsController.invalidate();

                snackbarController.addSnackbar({
                    id: 'unmap-monitor-success',
                    props: {
                        title: t`Test unmapped`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'unmap-monitor-error',
                    props: {
                        title: t`Failed to unmap monitor`,
                        description: t`An error occurred while unmapping the monitor. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isUnmapping(): boolean {
        return this.unmapMutation.isPending;
    }

    get hasError(): boolean {
        return this.unmapMutation.hasError;
    }

    unmapMonitorFromControl = async (
        testId: number,
        controlId: number,
    ): Promise<void> => {
        const { currentWorkspace, isLoading } = sharedWorkspacesController;

        await when(() => {
            return !isLoading;
        });

        if (!currentWorkspace) {
            throw new Error('Workspace not found');
        }

        return this.unmapMutation.mutateAsync({
            path: { xProductId: currentWorkspace.id },
            body: { testIds: [testId], controlIds: [controlId] },
        });
    };
}

export const sharedMonitoringUnmapMutationController =
    new MonitingUnmapMutationController();
