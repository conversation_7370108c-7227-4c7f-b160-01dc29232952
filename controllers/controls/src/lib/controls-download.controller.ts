import {
    grcControllerDownloadControlsOptions,
    grcControllerGetEvidenceOnlyOptions,
    grcControllerGetPolicyOnlyOptions,
} from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getCurrentDateString } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';

export type ControlsDownloadQuery = Required<
    Parameters<typeof grcControllerDownloadControlsOptions>
>[0]['query'];

class ControlsDownloadController {
    constructor() {
        makeAutoObservable(this);
    }

    downloadControlsQuery = new ObservedQuery(
        grcControllerDownloadControlsOptions,
    );

    downloadControlsEvidenceQuery = new ObservedQuery(
        grcControllerGetEvidenceOnlyOptions,
    );

    downloadControlPoliciesQuery = new ObservedQuery(
        grcControllerGetPolicyOnlyOptions,
    );

    initiateDownload = (query?: ControlsDownloadQuery) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.downloadControlsQuery.load({
            path: {
                xProductId: currentWorkspace.id,
            },
            query,
        });
    };

    get isLoading(): boolean {
        return this.downloadControlsQuery.isLoading;
    }

    get isDownloadControlsEvidenceLoading() {
        return this.downloadControlsEvidenceQuery.isLoading;
    }

    get isDownloadControlPoliciesLoading() {
        return this.downloadControlPoliciesQuery.isLoading;
    }

    downloadControls = (query?: ControlsDownloadQuery) => {
        this.initiateDownload(query);

        when(
            () => !this.isLoading,
            () => {
                const { data } = this.downloadControlsQuery;

                if (!data) {
                    return;
                }

                const blob = new Blob([data as unknown as string], {
                    type: 'text/csv',
                });

                downloadBlob(blob, `Controls-${getCurrentDateString()}.csv`);
            },
        );
    };

    downloadControlsEvidence = (controlId: number) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            throw new Error('No workspace found');
        }

        this.downloadControlsEvidenceQuery.load({
            path: {
                xProductId: currentWorkspace.id,
                controlId,
            },
        });
    };

    downloadControlPolicies = (controlId: number) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            throw new Error('No workspace found');
        }

        this.downloadControlPoliciesQuery.load({
            path: {
                controlId,
                xProductId: currentWorkspace.id,
            },
        });
    };
}

export const sharedControlsDownloadController =
    new ControlsDownloadController();
